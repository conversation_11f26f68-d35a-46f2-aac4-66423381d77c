/**
 * 动态组件使用示例
 * 演示如何使用 dynamicComponent.ts 中的功能
 */

import { dynamicComponentManager, createComponentFromJS } from '../dynamicComponent'
// import { store } from '../store' // 暂时注释掉，因为 store 导出有问题

// 示例 1: 基本使用 - 从当前编辑器中的代码创建组件
export async function createComponentFromCurrentEditor() {
  try {
    // 获取当前活动文件
    const activeFile = store.activeFile
    if (!activeFile || !activeFile.filename.endsWith('.vue')) {
      throw new Error('当前文件不是 Vue 组件')
    }
    
    // 解析 SFC
    const { descriptor, errors } = store.compiler.parse(activeFile.code, {
      filename: activeFile.filename
    })
    
    if (errors.length > 0) {
      throw new Error(`解析错误: ${errors.map(e => e.message).join(', ')}`)
    }
    
    // 创建动态组件
    const component = await dynamicComponentManager.createComponentFromDescriptor(descriptor)
    
    console.log('成功创建组件:', activeFile.filename)
    return component
    
  } catch (error) {
    console.error('创建组件失败:', error)
    throw error
  }
}

// 示例 2: 使用 Composition API
export function useCurrentFileComponent() {
  const activeFile = store.activeFile
  
  if (!activeFile || !activeFile.filename.endsWith('.vue')) {
    return {
      component: null,
      loading: false,
      error: new Error('当前文件不是 Vue 组件')
    }
  }
  
  // 解析 descriptor
  const { descriptor } = store.compiler.parse(activeFile.code, {
    filename: activeFile.filename
  })
  
  // 使用 composition API
  return useDynamicComponent(descriptor)
}

// 示例 3: 创建异步组件
export function createAsyncComponentFromCurrentFile() {
  const activeFile = store.activeFile
  
  if (!activeFile || !activeFile.filename.endsWith('.vue')) {
    throw new Error('当前文件不是 Vue 组件')
  }
  
  const { descriptor } = store.compiler.parse(activeFile.code, {
    filename: activeFile.filename
  })
  
  return createAsyncDynamicComponent(descriptor)
}

// 示例 4: 批量创建组件
export async function createAllVueComponents() {
  const descriptors = []
  
  // 收集所有 Vue 文件的 descriptor
  Object.entries(store.files).forEach(([filename, file]) => {
    if (filename.endsWith('.vue')) {
      const { descriptor, errors } = store.compiler.parse(file.code, { filename })
      if (errors.length === 0) {
        descriptors.push(descriptor)
      }
    }
  })
  
  // 批量创建组件
  const components = await createMultipleDynamicComponents(descriptors)
  
  console.log(`成功创建 ${components.size} 个组件:`, Array.from(components.keys()))
  return components
}

// 示例 5: 监听文件变化并自动更新组件
export function setupAutoComponentUpdate() {
  const componentCache = new Map()
  
  // 监听文件变化
  const unwatch = store.watch(
    () => store.files,
    (newFiles, oldFiles) => {
      Object.entries(newFiles).forEach(([filename, file]) => {
        if (filename.endsWith('.vue')) {
          const oldFile = oldFiles?.[filename]
          
          // 如果文件内容发生变化，清除缓存并重新创建组件
          if (!oldFile || oldFile.code !== file.code) {
            console.log(`文件 ${filename} 发生变化，重新创建组件`)
            
            // 清除旧缓存
            dynamicComponentManager.clearCache(filename)
            componentCache.delete(filename)
            
            // 异步创建新组件
            createComponentFromFile(filename, file)
              .then(component => {
                componentCache.set(filename, component)
                console.log(`组件 ${filename} 更新完成`)
              })
              .catch(error => {
                console.error(`更新组件 ${filename} 失败:`, error)
              })
          }
        }
      })
    },
    { deep: true }
  )
  
  return {
    componentCache,
    unwatch
  }
}

// 辅助函数：从文件创建组件
async function createComponentFromFile(filename, file) {
  const { descriptor, errors } = store.compiler.parse(file.code, { filename })
  
  if (errors.length > 0) {
    throw new Error(`解析 ${filename} 失败: ${errors.map(e => e.message).join(', ')}`)
  }
  
  return await dynamicComponentManager.createComponentFromDescriptor(descriptor)
}

// 示例 6: 创建带有默认 props 的组件渲染器
export function createComponentRenderer(defaultProps = {}) {
  return {
    async renderComponent(filename) {
      const file = store.files[filename]
      if (!file || !filename.endsWith('.vue')) {
        throw new Error(`文件 ${filename} 不存在或不是 Vue 组件`)
      }
      
      const component = await createComponentFromFile(filename, file)
      
      // 返回渲染函数
      return (props = {}) => {
        const mergedProps = { ...defaultProps, ...props }
        
        return {
          component,
          props: mergedProps,
          render() {
            return h(component, mergedProps)
          }
        }
      }
    }
  }
}

// 示例 7: 组件热更新
export function setupHotReload() {
  const activeComponents = new Map()
  
  // 监听编译完成事件
  const handleCompileComplete = (filename, compiledResult) => {
    if (filename.endsWith('.vue') && activeComponents.has(filename)) {
      console.log(`热更新组件: ${filename}`)
      
      // 清除缓存
      dynamicComponentManager.clearCache(filename)
      
      // 重新创建组件
      const file = store.files[filename]
      if (file) {
        createComponentFromFile(filename, file)
          .then(newComponent => {
            activeComponents.set(filename, newComponent)
            
            // 触发组件更新事件
            window.dispatchEvent(new CustomEvent('component-hot-reload', {
              detail: { filename, component: newComponent }
            }))
          })
          .catch(error => {
            console.error(`热更新失败 ${filename}:`, error)
          })
      }
    }
  }
  
  // 注册组件
  const registerComponent = (filename, component) => {
    activeComponents.set(filename, component)
  }
  
  // 注销组件
  const unregisterComponent = (filename) => {
    activeComponents.delete(filename)
    dynamicComponentManager.clearCache(filename)
  }
  
  return {
    handleCompileComplete,
    registerComponent,
    unregisterComponent,
    activeComponents: () => new Map(activeComponents)
  }
}

// 导出所有示例函数
export default {
  createComponentFromCurrentEditor,
  useCurrentFileComponent,
  createAsyncComponentFromCurrentFile,
  createAllVueComponents,
  setupAutoComponentUpdate,
  createComponentRenderer,
  setupHotReload
}
