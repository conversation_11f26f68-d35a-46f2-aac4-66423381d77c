{
  $schema: 'https://docs.renovatebot.com/renovate-schema.json',
  extends: [
    'config:base',
    'schedule:weekly',
    'group:allNonMajor',
    ':semanticCommitTypeAll(chore)',
  ],
  labels: ['dependencies'],
  rangeStrategy: 'bump',
  packageRules: [
    {
      depTypeList: ['peerDependencies'],
      enabled: false,
    },
    {
      matchPackageNames: ['codemirror'],
      matchUpdateTypes: ['major'],
      enabled: false,
    },
  ],
  postUpdateOptions: ['pnpmDedupe'],
}
