# 动态组件系统

这个动态组件系统允许你将 SFC (Single File Component) 编译器解析后的 `descriptor` 内容直接转换为可用的 Vue 组件，并在运行时动态渲染。

## 核心文件

- `src/dynamicComponent.ts` - 核心动态组件管理器
- `src/components/DynamicComponentRenderer.vue` - 可视化渲染器组件
- `src/examples/dynamicComponentExample.js` - 使用示例

## 主要功能

### 1. DynamicComponentManager 类

核心管理器，负责将 SFC descriptor 转换为 Vue 组件。

```javascript
import { dynamicComponentManager } from './dynamicComponent'

// 从 descriptor 创建组件
const component = await dynamicComponentManager.createComponentFromDescriptor(descriptor)

// 清除缓存
dynamicComponentManager.clearCache()

// 获取缓存的组件
const cachedComponent = dynamicComponentManager.getCachedComponent('App.vue')
```

### 2. Composition API 支持

```javascript
import { useDynamicComponent } from './dynamicComponent'

// 在组件中使用
const { component, loading, error, reload } = useDynamicComponent(descriptor)
```

### 3. 异步组件支持

```javascript
import { createAsyncDynamicComponent } from './dynamicComponent'

// 创建异步组件
const AsyncComponent = createAsyncDynamicComponent(descriptor)
```

## 使用方法

### 基本使用

```javascript
// 1. 解析 SFC 文件
const { descriptor } = store.compiler.parse(sfcCode, { filename: 'App.vue' })

// 2. 创建动态组件
const component = await dynamicComponentManager.createComponentFromDescriptor(descriptor)

// 3. 在模板中使用
<component :is="component" :dataChart="chartData" :refList="listData" />
```

### 在 Vue 组件中使用

```vue
<template>
  <div>
    <component 
      v-if="dynamicComponent" 
      :is="dynamicComponent" 
      v-bind="props"
    />
    <div v-else-if="loading">Loading...</div>
    <div v-else-if="error">Error: {{ error.message }}</div>
  </div>
</template>

<script setup>
import { useDynamicComponent } from '../dynamicComponent'

// 假设你有一个 descriptor
const { component: dynamicComponent, loading, error } = useDynamicComponent(descriptor)

const props = {
  dataChart: { type: 'bar', data: [1, 2, 3] },
  refList: ['item1', 'item2', 'item3']
}
</script>
```

### 批量创建组件

```javascript
import { createMultipleDynamicComponents } from './dynamicComponent'

// 批量创建
const descriptors = [descriptor1, descriptor2, descriptor3]
const components = await createMultipleDynamicComponents(descriptors)

// 使用
components.forEach((component, filename) => {
  console.log(`Component ${filename} created:`, component)
})
```

## 可视化渲染器

使用 `DynamicComponentRenderer.vue` 组件可以可视化地管理和渲染动态组件：

```vue
<template>
  <DynamicComponentRenderer />
</template>

<script setup>
import DynamicComponentRenderer from './components/DynamicComponentRenderer.vue'
</script>
```

### 渲染器功能

- 📁 **文件选择**: 从可用的 Vue 文件中选择要渲染的组件
- 🔄 **自动刷新**: 监听文件变化并自动重新编译组件
- 🗂️ **批量渲染**: 同时渲染多个组件进行对比
- 🧹 **缓存管理**: 清除组件缓存，强制重新编译
- 🐛 **调试模式**: 显示详细的调试信息
- ⚡ **热更新**: 文件变化时自动更新组件

## 工作原理

1. **解析阶段**: 使用 Vue SFC 编译器解析 `.vue` 文件，得到 `descriptor`
2. **重构阶段**: 从 `descriptor` 重新构建完整的 SFC 源码
3. **编译阶段**: 使用现有的编译流程将 SFC 编译为 JavaScript
4. **执行阶段**: 在安全的环境中执行编译后的代码，提取组件对象
5. **缓存阶段**: 缓存组件以提高性能

## 支持的功能

✅ **Script Setup 语法**
✅ **Options API 语法**  
✅ **TypeScript 支持**
✅ **Scoped CSS**
✅ **CSS Modules**
✅ **自定义块**
✅ **Props 自动注入**
✅ **热更新**
✅ **错误处理**
✅ **缓存机制**

## 错误处理

系统包含完善的错误处理机制：

```javascript
try {
  const component = await dynamicComponentManager.createComponentFromDescriptor(descriptor)
  // 使用组件
} catch (error) {
  console.error('创建组件失败:', error.message)
  // 处理错误
}
```

## 性能优化

- **组件缓存**: 避免重复编译相同的组件
- **懒加载**: 使用异步组件按需加载
- **批量处理**: 支持批量创建多个组件
- **内存管理**: 提供缓存清理机制

## 注意事项

1. **安全性**: 动态执行代码存在安全风险，仅在可信环境中使用
2. **性能**: 大量动态组件可能影响性能，建议使用缓存
3. **兼容性**: 需要支持 ES6+ 的现代浏览器
4. **调试**: 动态组件的调试可能比静态组件复杂

## 示例场景

- **在线代码编辑器**: 实时预览用户编写的 Vue 组件
- **组件库预览**: 动态展示组件库中的所有组件
- **主题系统**: 根据配置动态生成不同样式的组件
- **A/B 测试**: 动态切换不同版本的组件
- **插件系统**: 动态加载第三方组件

## 在 Vue SFC Playground 中使用

### 1. 启用动态组件渲染器

在输出面板中，你会看到一个新的 "dynamic" 标签页，点击它即可打开动态组件渲染器。

### 2. 测试组件

1. 在编辑器中创建或编辑 `.vue` 文件
2. 切换到 "dynamic" 输出模式
3. 选择要渲染的组件文件
4. 查看组件的实时渲染效果

### 3. 使用测试组件

项目中包含了一个测试组件 `src/template/dynamic-test.vue`，它演示了：
- Props 接收和显示
- 交互功能（按钮、输入框）
- 数据可视化（简单图表）
- 列表渲染

### 4. 自动 Props 注入

系统会自动为组件注入以下 props：
- `dataChart`: 图表数据对象
- `refList`: 引用列表数组

## 扩展功能

可以基于这个系统扩展更多功能：

- **组件版本管理**: 支持组件的多个版本
- **依赖注入**: 自动解析和注入组件依赖
- **性能监控**: 监控组件渲染性能
- **自动测试**: 自动生成组件测试用例
- **组件库集成**: 与组件库系统集成
- **主题切换**: 支持动态主题切换
