<template>
  <div class="inject-preview-renderer">
    <div class="header">
      <h3>Props 注入预览</h3>
      <div class="controls">
        <button @click="refreshComponent" :disabled="loading">
          {{ loading ? '刷新中...' : '刷新组件' }}
        </button>
        <button @click="toggleComparison">
          {{ showComparison ? '隐藏对比' : '显示对比' }}
        </button>
      </div>
    </div>

    <div class="content">
      <!-- 单组件模式 -->
      <div v-if="!showComparison" class="single-mode">
        <div class="component-section">
          <h4>注入后的组件</h4>
          <div class="component-wrapper">
            <component 
              v-if="injectedComponent" 
              :is="injectedComponent"
              v-bind="defaultProps"
              @error="onComponentError"
            />
            <div v-else-if="loading" class="loading">
              <div class="spinner"></div>
              <p>正在编译组件...</p>
            </div>
            <div v-else-if="error" class="error">
              <h5>编译错误:</h5>
              <pre>{{ error }}</pre>
            </div>
            <div v-else class="empty">
              <p>没有可渲染的组件</p>
              <p>请确保当前文件是有效的 Vue 组件</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 对比模式 -->
      <div v-else class="comparison-mode">
        <div class="comparison-grid">
          <!-- 原始组件 -->
          <div class="component-section">
            <h4>原始组件</h4>
            <div class="component-wrapper">
              <component 
                v-if="originalComponent" 
                :is="originalComponent"
                v-bind="defaultProps"
                @error="(err) => onComponentError(err, 'original')"
              />
              <div v-else class="empty">
                <p>原始组件编译失败</p>
              </div>
            </div>
          </div>

          <!-- 注入后组件 -->
          <div class="component-section">
            <h4>注入后组件</h4>
            <div class="component-wrapper">
              <component 
                v-if="injectedComponent" 
                :is="injectedComponent"
                v-bind="defaultProps"
                @error="(err) => onComponentError(err, 'injected')"
              />
              <div v-else class="empty">
                <p>注入组件编译失败</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 代码对比 -->
      <div class="code-comparison" v-if="showCodeDiff">
        <h4>代码变化</h4>
        <div class="code-diff">
          <div class="code-section">
            <h5>原始代码</h5>
            <pre class="code-block">{{ originalCode }}</pre>
          </div>
          <div class="code-section">
            <h5>注入后代码</h5>
            <pre class="code-block">{{ injectedCode }}</pre>
          </div>
        </div>
      </div>

      <!-- Props 配置 -->
      <div class="props-config">
        <h4>Props 配置</h4>
        <div class="props-editor">
          <div class="prop-group">
            <label>dataChart:</label>
            <textarea 
              v-model="dataChartJson" 
              @input="updateDataChart"
              placeholder="输入 JSON 格式的图表数据"
            ></textarea>
          </div>
          <div class="prop-group">
            <label>refList:</label>
            <textarea 
              v-model="refListJson" 
              @input="updateRefList"
              placeholder="输入 JSON 格式的列表数据"
            ></textarea>
          </div>
        </div>
      </div>
    </div>

    <!-- 调试信息 -->
    <div class="debug-panel" v-if="showDebug">
      <h4>调试信息</h4>
      <div class="debug-content">
        <p><strong>当前文件:</strong> {{ currentFilename }}</p>
        <p><strong>注入状态:</strong> {{ injectionStatus }}</p>
        <p><strong>编译时间:</strong> {{ compilationTime }}ms</p>
        <p><strong>组件状态:</strong> {{ componentStatus }}</p>
      </div>
    </div>

    <button class="debug-toggle" @click="showDebug = !showDebug">
      {{ showDebug ? '隐藏' : '显示' }}调试
    </button>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { store } from '../store'
import { dynamicComponentManager } from '../dynamicComponent'
import { injectRefListToSFC } from '../transform'

// 响应式数据
const loading = ref(false)
const error = ref(null)
const originalComponent = ref(null)
const injectedComponent = ref(null)
const originalCode = ref('')
const injectedCode = ref('')
const showComparison = ref(false)
const showCodeDiff = ref(false)
const showDebug = ref(false)
const compilationTime = ref(0)

// Props 配置
const dataChartJson = ref(JSON.stringify({
  type: 'bar',
  data: [10, 20, 30, 40, 50],
  labels: ['A', 'B', 'C', 'D', 'E']
}, null, 2))

const refListJson = ref(JSON.stringify([
  { id: 1, name: 'Item 1', value: 100 },
  { id: 2, name: 'Item 2', value: 200 },
  { id: 3, name: 'Item 3', value: 300 }
], null, 2))

// 计算属性
const currentFilename = computed(() => store.activeFile?.filename || '')

const defaultProps = computed(() => {
  try {
    return {
      dataChart: JSON.parse(dataChartJson.value),
      refList: JSON.parse(refListJson.value)
    }
  } catch (e) {
    return {
      dataChart: {},
      refList: []
    }
  }
})

const injectionStatus = computed(() => {
  if (!originalCode.value || !injectedCode.value) return '未处理'
  return originalCode.value === injectedCode.value ? '无变化' : '已注入'
})

const componentStatus = computed(() => {
  const original = originalComponent.value ? '✓' : '✗'
  const injected = injectedComponent.value ? '✓' : '✗'
  return `原始:${original} 注入:${injected}`
})

// 方法
const refreshComponent = async () => {
  const activeFile = store.activeFile
  if (!activeFile || !activeFile.filename.endsWith('.vue')) {
    error.value = '当前文件不是 Vue 组件'
    return
  }

  loading.value = true
  error.value = null
  const startTime = performance.now()

  try {
    // 获取原始代码
    originalCode.value = activeFile.code

    // 执行 props 注入
    injectedCode.value = injectRefListToSFC(activeFile.code, activeFile.filename)

    // 创建原始组件
    if (showComparison.value) {
      try {
        const { descriptor: originalDescriptor } = store.compiler.parse(originalCode.value, {
          filename: activeFile.filename
        })
        originalComponent.value = await dynamicComponentManager.createComponentFromDescriptor(originalDescriptor)
      } catch (err) {
        console.warn('原始组件创建失败:', err)
        originalComponent.value = null
      }
    }

    // 创建注入后的组件
    const { descriptor: injectedDescriptor, errors } = store.compiler.parse(injectedCode.value, {
      filename: activeFile.filename
    })

    if (errors.length > 0) {
      throw new Error(`解析错误: ${errors.map(e => e.message).join(', ')}`)
    }

    injectedComponent.value = await dynamicComponentManager.createComponentFromDescriptor(injectedDescriptor)

    compilationTime.value = Math.round(performance.now() - startTime)
    console.log(`[InjectPreview] 组件创建成功，耗时 ${compilationTime.value}ms`)

  } catch (err) {
    error.value = err.message
    console.error('[InjectPreview] 组件创建失败:', err)
  } finally {
    loading.value = false
  }
}

const toggleComparison = () => {
  showComparison.value = !showComparison.value
  if (showComparison.value) {
    refreshComponent()
  }
}

const updateDataChart = () => {
  try {
    JSON.parse(dataChartJson.value)
  } catch (e) {
    console.warn('dataChart JSON 格式错误:', e.message)
  }
}

const updateRefList = () => {
  try {
    JSON.parse(refListJson.value)
  } catch (e) {
    console.warn('refList JSON 格式错误:', e.message)
  }
}

const onComponentError = (err, type = 'injected') => {
  console.error(`[InjectPreview] ${type} 组件运行时错误:`, err)
  error.value = `${type} 组件错误: ${err.message}`
}

// 监听文件变化
watch(() => store.activeFile, () => {
  nextTick(() => {
    refreshComponent()
  })
}, { deep: true })

// 组件挂载时初始化
onMounted(() => {
  refreshComponent()
})
</script>

<style scoped>
.inject-preview-renderer {
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid var(--border);
  background: var(--bg);
}

.header h3 {
  margin: 0;
  font-size: 14px;
  color: var(--text-light);
}

.controls {
  display: flex;
  gap: 8px;
}

.controls button {
  padding: 6px 12px;
  font-size: 12px;
  border: 1px solid var(--border);
  background: var(--bg);
  color: var(--text-light);
  border-radius: 4px;
  cursor: pointer;
}

.controls button:hover {
  background: var(--bg-soft);
}

.controls button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.content {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.comparison-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 20px;
}

.component-section {
  border: 1px solid var(--border);
  border-radius: 6px;
  overflow: hidden;
}

.component-section h4 {
  margin: 0;
  padding: 10px 15px;
  background: var(--bg-soft);
  border-bottom: 1px solid var(--border);
  font-size: 13px;
  color: var(--text-light);
}

.component-wrapper {
  padding: 15px;
  min-height: 200px;
  background: var(--bg);
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 150px;
}

.spinner {
  width: 30px;
  height: 30px;
  border: 3px solid var(--border);
  border-top: 3px solid var(--color-branding);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  color: #dc3545;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  padding: 10px;
}

.error h5 {
  margin: 0 0 5px 0;
}

.error pre {
  margin: 0;
  font-size: 12px;
  white-space: pre-wrap;
}

.empty {
  text-align: center;
  color: var(--text-light);
  padding: 40px 20px;
}

.code-comparison {
  margin-top: 20px;
  border: 1px solid var(--border);
  border-radius: 6px;
}

.code-comparison h4 {
  margin: 0;
  padding: 10px 15px;
  background: var(--bg-soft);
  border-bottom: 1px solid var(--border);
  font-size: 13px;
}

.code-diff {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1px;
  background: var(--border);
}

.code-section {
  background: var(--bg);
  padding: 10px;
}

.code-section h5 {
  margin: 0 0 10px 0;
  font-size: 12px;
  color: var(--text-light);
}

.code-block {
  margin: 0;
  padding: 10px;
  background: var(--bg-soft);
  border-radius: 4px;
  font-size: 11px;
  font-family: var(--font-code);
  overflow-x: auto;
  max-height: 200px;
  overflow-y: auto;
}

.props-config {
  margin-top: 20px;
  border: 1px solid var(--border);
  border-radius: 6px;
}

.props-config h4 {
  margin: 0;
  padding: 10px 15px;
  background: var(--bg-soft);
  border-bottom: 1px solid var(--border);
  font-size: 13px;
}

.props-editor {
  padding: 15px;
}

.prop-group {
  margin-bottom: 15px;
}

.prop-group label {
  display: block;
  margin-bottom: 5px;
  font-size: 12px;
  font-weight: bold;
  color: var(--text-light);
}

.prop-group textarea {
  width: 100%;
  height: 80px;
  padding: 8px;
  border: 1px solid var(--border);
  border-radius: 4px;
  font-family: var(--font-code);
  font-size: 11px;
  background: var(--bg);
  color: inherit;
  resize: vertical;
}

.debug-panel {
  margin-top: 20px;
  padding: 15px;
  background: var(--bg-soft);
  border: 1px solid var(--border);
  border-radius: 6px;
}

.debug-panel h4 {
  margin: 0 0 10px 0;
  font-size: 13px;
}

.debug-content p {
  margin: 5px 0;
  font-size: 12px;
  font-family: var(--font-code);
}

.debug-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 8px 12px;
  background: var(--color-branding);
  color: white;
  border: none;
  border-radius: 15px;
  cursor: pointer;
  font-size: 12px;
  z-index: 1000;
}

.debug-toggle:hover {
  opacity: 0.8;
}
</style>
