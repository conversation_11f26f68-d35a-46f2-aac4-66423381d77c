{"compilerOptions": {"outDir": "dist", "sourceMap": false, "target": "esnext", "useDefineForClassFields": false, "module": "esnext", "moduleResolution": "bundler", "allowJs": false, "strict": true, "noUnusedLocals": true, "experimentalDecorators": true, "resolveJsonModule": true, "esModuleInterop": true, "removeComments": false, "lib": ["esnext", "dom"], "jsx": "preserve", "rootDir": ".", "skipLibCheck": true}, "include": ["src", "test", "vite.config.ts"], "exclude": ["src/template"]}