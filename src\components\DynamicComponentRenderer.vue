<template>
  <div class="dynamic-component-renderer">
    <div class="header">
      <h2>动态组件渲染器</h2>
      <div class="controls">
        <button @click="refreshComponents" :disabled="loading">
          {{ loading ? '刷新中...' : '刷新组件' }}
        </button>
        <button @click="clearCache">清除缓存</button>
        <select v-model="selectedFile" @change="onFileChange">
          <option value="">选择文件</option>
          <option v-for="filename in availableFiles" :key="filename" :value="filename">
            {{ filename }}
          </option>
        </select>
      </div>
    </div>

    <div class="content">
      <!-- 单个组件渲染 -->
      <div v-if="selectedFile && currentComponent" class="single-component">
        <h3>当前组件: {{ selectedFile }}</h3>
        <div class="component-wrapper">
          <component 
            :is="currentComponent" 
            v-bind="componentProps"
            @error="onComponentError"
          />
        </div>
        <div v-if="componentError" class="error">
          组件错误: {{ componentError.message }}
        </div>
      </div>

      <!-- 批量组件渲染 -->
      <div v-else class="multiple-components">
        <h3>所有组件 ({{ Object.keys(allComponents).length }})</h3>
        <div class="components-grid">
          <div 
            v-for="(component, filename) in allComponents" 
            :key="filename"
            class="component-card"
          >
            <div class="component-header">
              <h4>{{ filename }}</h4>
              <button @click="selectFile(filename)" class="select-btn">选择</button>
            </div>
            <div class="component-content">
              <component 
                :is="component" 
                v-bind="componentProps"
                @error="(err) => onComponentError(err, filename)"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading">
        <div class="spinner"></div>
        <p>正在加载组件...</p>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && Object.keys(allComponents).length === 0" class="empty">
        <p>没有找到可渲染的组件</p>
        <p>请确保有 .vue 文件并且编译成功</p>
      </div>
    </div>

    <!-- 调试信息 -->
    <div class="debug-info" v-if="showDebug">
      <h3>调试信息</h3>
      <div class="debug-content">
        <h4>可用文件:</h4>
        <pre>{{ JSON.stringify(availableFiles, null, 2) }}</pre>
        
        <h4>组件缓存:</h4>
        <pre>{{ JSON.stringify(Object.keys(allComponents), null, 2) }}</pre>
        
        <h4>当前 Props:</h4>
        <pre>{{ JSON.stringify(componentProps, null, 2) }}</pre>
      </div>
    </div>

    <!-- 调试开关 -->
    <button 
      class="debug-toggle" 
      @click="showDebug = !showDebug"
      :class="{ active: showDebug }"
    >
      {{ showDebug ? '隐藏' : '显示' }}调试
    </button>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick, inject } from 'vue'
import { dynamicComponentManager, createComponentFromJS } from '../dynamicComponent'
import { injectKeyProps } from '../types'

// 获取 store
const { store } = inject(injectKeyProps) || {}

// 响应式数据
const loading = ref(false)
const selectedFile = ref('')
const allComponents = ref({})
const componentError = ref(null)
const showDebug = ref(false)

// 默认 props
const componentProps = ref({
  dataChart: {
    type: 'bar',
    data: [10, 20, 30, 40, 50],
    labels: ['A', 'B', 'C', 'D', 'E']
  },
  refList: [
    { id: 1, name: 'Item 1', value: 100 },
    { id: 2, name: 'Item 2', value: 200 },
    { id: 3, name: 'Item 3', value: 300 }
  ]
})

// 计算属性
const availableFiles = computed(() => {
  return Object.keys(store.files).filter(filename => 
    filename.endsWith('.vue') && store.files[filename].compiled?.js
  )
})

const currentComponent = computed(() => {
  return selectedFile.value ? allComponents.value[selectedFile.value] : null
})

// 方法
const refreshComponents = async () => {
  loading.value = true
  componentError.value = null
  
  try {
    console.log('[DynamicRenderer] Refreshing components...')
    
    // 清除缓存
    dynamicComponentManager.clearCache()
    
    // 重新创建所有组件
    const newComponents = {}
    
    for (const filename of availableFiles.value) {
      try {
        const file = store.files[filename]
        if (file.compiled?.js) {
          // 解析 descriptor
          const { descriptor } = store.compiler.parse(file.code, { filename })
          
          if (descriptor) {
            const component = await dynamicComponentManager.createComponentFromDescriptor(descriptor)
            newComponents[filename] = component
            console.log(`[DynamicRenderer] Created component for ${filename}`)
          }
        }
      } catch (error) {
        console.error(`[DynamicRenderer] Failed to create component for ${filename}:`, error)
      }
    }
    
    allComponents.value = newComponents
    console.log(`[DynamicRenderer] Successfully created ${Object.keys(newComponents).length} components`)
    
  } catch (error) {
    console.error('[DynamicRenderer] Failed to refresh components:', error)
    componentError.value = error
  } finally {
    loading.value = false
  }
}

const clearCache = () => {
  dynamicComponentManager.clearCache()
  allComponents.value = {}
  selectedFile.value = ''
  componentError.value = null
  console.log('[DynamicRenderer] Cache cleared')
}

const selectFile = (filename) => {
  selectedFile.value = filename
  componentError.value = null
}

const onFileChange = () => {
  componentError.value = null
}

const onComponentError = (error, filename = selectedFile.value) => {
  console.error(`[DynamicRenderer] Component error in ${filename}:`, error)
  componentError.value = error
}

// 监听文件变化
watch(() => store.files, () => {
  nextTick(() => {
    refreshComponents()
  })
}, { deep: true })

// 组件挂载时初始化
onMounted(() => {
  refreshComponents()
})
</script>

<style scoped>
.dynamic-component-renderer {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.controls button, .controls select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.controls button:hover {
  background: #f5f5f5;
}

.controls button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.single-component {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.component-wrapper {
  margin-top: 15px;
  padding: 15px;
  border: 1px dashed #ccc;
  border-radius: 4px;
  background: #fafafa;
}

.components-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.component-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.component-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.component-header h4 {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.select-btn {
  padding: 4px 8px;
  font-size: 12px;
  border: 1px solid #007bff;
  background: #007bff;
  color: white;
  border-radius: 3px;
  cursor: pointer;
}

.select-btn:hover {
  background: #0056b3;
}

.component-content {
  padding: 15px;
}

.loading {
  text-align: center;
  padding: 40px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty {
  text-align: center;
  padding: 40px;
  color: #666;
}

.error {
  color: #dc3545;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  padding: 10px;
  margin-top: 10px;
}

.debug-info {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
}

.debug-content pre {
  background: #fff;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}

.debug-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 10px 15px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  z-index: 1000;
}

.debug-toggle.active {
  background: #28a745;
}

.debug-toggle:hover {
  opacity: 0.8;
}
</style>
