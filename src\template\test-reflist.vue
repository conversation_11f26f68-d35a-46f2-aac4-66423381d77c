<template>
  <div class="container">
    <h1>RefList 自动注入测试</h1>
    
    <div class="test-section">
      <h2>测试1: 无props组件</h2>
      <p>这个组件没有定义任何props，应该自动注入refList</p>
      <p>Props: {{ JSON.stringify($props, null, 2) }}</p>
    </div>
    
    <div class="test-section">
      <h2>测试2: 检查refList类型</h2>
      <p>RefList类型: {{ typeof refList }}</p>
      <p>RefList是否为数组: {{ Array.isArray(refList) }}</p>
      <p>RefList内容: {{ JSON.stringify(refList) }}</p>
    </div>
    
    <div class="test-section">
      <h2>测试3: 使用refList</h2>
      <button @click="addToRefList">添加项目到RefList</button>
      <ul v-if="refList && refList.length">
        <li v-for="(item, index) in refList" :key="index">
          {{ item }}
        </li>
      </ul>
      <p v-else>RefList为空</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 注意：这里没有定义props，refList应该被自动注入

const addToRefList = () => {
  if (refList && Array.isArray(refList)) {
    refList.push(`Item ${refList.length + 1}`)
  } else {
    console.warn('RefList不可用或不是数组')
  }
}

// 在组件挂载时输出props信息
console.log('Component props:', $props)
</script>

<style scoped>
.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.test-section {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

h1 {
  color: #2c3e50;
  text-align: center;
}

h2 {
  color: #34495e;
  border-bottom: 2px solid #3498db;
  padding-bottom: 5px;
}

button {
  background: #3498db;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 5px;
  cursor: pointer;
  margin: 10px 0;
}

button:hover {
  background: #2980b9;
}

ul {
  background: white;
  padding: 10px;
  border-radius: 5px;
  margin: 10px 0;
}

li {
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

li:last-child {
  border-bottom: none;
}

p {
  background: white;
  padding: 10px;
  border-radius: 5px;
  margin: 10px 0;
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
}
</style>
