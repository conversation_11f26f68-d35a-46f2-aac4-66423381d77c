<template>
  <div>
    <h1>RefList注入测试用例</h1>
    
    <!-- 测试用例1: 没有props的组件 -->
    <TestCase1 />
    
    <!-- 测试用例2: 有其他props的组件 -->
    <TestCase2 :message="'Hello'" />
    
    <!-- 测试用例3: 已经有refList的组件 -->
    <TestCase3 :refList="[1,2,3]" />
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 测试用例1: 没有props的组件
const TestCase1 = {
  template: `
    <div class="test-case">
      <h3>测试用例1: 无props组件</h3>
      <p>Props: {{ JSON.stringify($props) }}</p>
    </div>
  `
}

// 测试用例2: 有其他props的组件  
const TestCase2 = {
  props: {
    message: String
  },
  template: `
    <div class="test-case">
      <h3>测试用例2: 有其他props的组件</h3>
      <p>Message: {{ message }}</p>
      <p>Props: {{ JSON.stringify($props) }}</p>
    </div>
  `
}

// 测试用例3: 已经有refList的组件
const TestCase3 = {
  props: {
    refList: {
      type: Array,
      default: () => []
    }
  },
  template: `
    <div class="test-case">
      <h3>测试用例3: 已有refList的组件</h3>
      <p>RefList: {{ JSON.stringify(refList) }}</p>
      <p>Props: {{ JSON.stringify($props) }}</p>
    </div>
  `
}
</script>

<style scoped>
.test-case {
  margin: 10px 0;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  background: #f9f9f9;
}

h3 {
  margin-top: 0;
  color: #333;
}

p {
  margin: 5px 0;
  font-family: monospace;
  background: white;
  padding: 5px;
  border-radius: 3px;
}
</style>
