<script setup>
import { ref } from 'vue'

const msg = ref('Hello World!')
const testResults = ref([])

// 测试默认props是否被自动注入
console.log('=== Welcome Component Props Test ===')
console.log('Component loaded, checking props...')
console.log('$props:', $props)
try {
  console.log('DataChart:', dataChart)
  console.log('DataChart type:', typeof dataChart)
} catch (e) {
  console.log('DataChart error:', e.message)
}
try {
  console.log('RefList:', refList)
  console.log('RefList type:', typeof refList)
} catch (e) {
  console.log('RefList error:', e.message)
}
console.log('=====================================')

// 立即执行一次测试
setTimeout(() => {
  testProps()
}, 100)

const testProps = () => {
  testResults.value = []

  // 测试dataChart
  if (typeof dataChart !== 'undefined') {
    testResults.value.push(`✅ dataChart已注入，类型: ${typeof dataChart}`)
  } else {
    testResults.value.push('❌ dataChart未注入')
  }

  // 测试refList
  if (typeof refList !== 'undefined') {
    testResults.value.push(`✅ refList已注入，类型: ${typeof refList}`)
    if (Array.isArray(refList)) {
      testResults.value.push('✅ refList是数组类型')
    } else if (typeof refList === 'object') {
      testResults.value.push('✅ refList是对象类型')
    }
  } else {
    testResults.value.push('❌ refList未注入')
  }
}

const logProps = () => {
  console.log('=== Props详细信息 ===')
  console.log('所有props:', $props)
  console.log('dataChart:', dataChart)
  console.log('refList:', refList)
  console.log('dataChart类型:', typeof dataChart)
  console.log('refList类型:', typeof refList)
  console.log('refList是否为数组:', Array.isArray(refList))
  console.log('==================')
}
</script>

<template>
  <div>
    <h1>{{ msg }}</h1>
    <input v-model="msg" />

    <div style="margin-top: 20px; padding: 15px; border: 1px solid #ccc; border-radius: 5px; background: #f9f9f9;">
      <h3>默认Props自动注入测试</h3>
      <p><strong>所有Props:</strong></p>
      <pre style="background: white; padding: 10px; border-radius: 3px;">{{ JSON.stringify($props, null, 2) }}</pre>

      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 15px;">
        <div style="background: white; padding: 10px; border-radius: 5px;">
          <h4 style="margin-top: 0; color: #2c3e50;">DataChart 测试</h4>
          <p><strong>值:</strong> {{ dataChart || '未定义' }}</p>
          <p><strong>类型:</strong> {{ typeof dataChart }}</p>
          <p><strong>是否为对象:</strong> {{ typeof dataChart === 'object' && dataChart !== null }}</p>
        </div>

        <div style="background: white; padding: 10px; border-radius: 5px;">
          <h4 style="margin-top: 0; color: #2c3e50;">RefList 测试</h4>
          <p><strong>值:</strong> {{ refList || '未定义' }}</p>
          <p><strong>类型:</strong> {{ typeof refList }}</p>
          <p><strong>是否为数组:</strong> {{ Array.isArray(refList) }}</p>
        </div>
      </div>

      <div style="margin-top: 15px;">
        <button @click="testProps" style="background: #42b883; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 10px;">
          测试Props功能
        </button>
        <button @click="logProps" style="background: #3498db; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
          在控制台输出Props
        </button>
      </div>

      <div v-if="testResults.length > 0" style="margin-top: 15px;">
        <h4>测试结果:</h4>
        <ul style="background: white; padding: 10px; border-radius: 5px;">
          <li v-for="(result, index) in testResults" :key="index">{{ result }}</li>
        </ul>
      </div>
    </div>
  </div>
</template>
