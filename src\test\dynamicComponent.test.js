/**
 * 动态组件系统集成测试
 * 测试从 SFC descriptor 到动态组件的完整流程
 */

import { dynamicComponentManager, useDynamicComponent, createAsyncDynamicComponent } from '../dynamicComponent'
import { store } from '../store'

// 模拟 SFC 代码
const mockSFCCode = `
<template>
  <div class="test-component">
    <h1>{{ title }}</h1>
    <p>DataChart: {{ dataChart }}</p>
    <p>RefList: {{ refList }}</p>
    <button @click="increment">Count: {{ count }}</button>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
  dataChart: Object,
  refList: Array
})

const title = ref('Test Component')
const count = ref(0)

const increment = () => {
  count.value++
}
</script>

<style scoped>
.test-component {
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 8px;
}
</style>
`

// 测试用例
export const dynamicComponentTests = {
  
  // 测试 1: 基本的 descriptor 解析和组件创建
  async testBasicComponentCreation() {
    console.log('🧪 测试 1: 基本组件创建')
    
    try {
      // 解析 SFC
      const { descriptor, errors } = store.compiler.parse(mockSFCCode, {
        filename: 'TestComponent.vue'
      })
      
      if (errors.length > 0) {
        throw new Error(`解析错误: ${errors.map(e => e.message).join(', ')}`)
      }
      
      // 创建组件
      const component = await dynamicComponentManager.createComponentFromDescriptor(descriptor)
      
      console.log('✅ 组件创建成功:', component)
      return { success: true, component }
      
    } catch (error) {
      console.error('❌ 组件创建失败:', error)
      return { success: false, error: error.message }
    }
  },
  
  // 测试 2: 缓存机制
  async testCaching() {
    console.log('🧪 测试 2: 缓存机制')
    
    try {
      const { descriptor } = store.compiler.parse(mockSFCCode, {
        filename: 'CacheTest.vue'
      })
      
      // 第一次创建
      const start1 = performance.now()
      const component1 = await dynamicComponentManager.createComponentFromDescriptor(descriptor)
      const time1 = performance.now() - start1
      
      // 第二次创建（应该使用缓存）
      const start2 = performance.now()
      const component2 = await dynamicComponentManager.createComponentFromDescriptor(descriptor)
      const time2 = performance.now() - start2
      
      const isCached = component1 === component2
      const isFaster = time2 < time1
      
      console.log(`✅ 缓存测试: 相同组件=${isCached}, 更快=${isFaster}`)
      console.log(`   第一次: ${time1.toFixed(2)}ms, 第二次: ${time2.toFixed(2)}ms`)
      
      return { 
        success: true, 
        isCached, 
        isFaster, 
        times: { first: time1, second: time2 }
      }
      
    } catch (error) {
      console.error('❌ 缓存测试失败:', error)
      return { success: false, error: error.message }
    }
  },
  
  // 测试 3: Composition API
  async testCompositionAPI() {
    console.log('🧪 测试 3: Composition API')
    
    try {
      const { descriptor } = store.compiler.parse(mockSFCCode, {
        filename: 'CompositionTest.vue'
      })
      
      // 使用 composition API
      const { component, loading, error } = useDynamicComponent(descriptor)
      
      // 等待组件加载
      let attempts = 0
      while (loading.value && attempts < 50) {
        await new Promise(resolve => setTimeout(resolve, 100))
        attempts++
      }
      
      if (error.value) {
        throw error.value
      }
      
      if (!component.value) {
        throw new Error('组件未加载')
      }
      
      console.log('✅ Composition API 测试成功')
      return { success: true, component: component.value }
      
    } catch (error) {
      console.error('❌ Composition API 测试失败:', error)
      return { success: false, error: error.message }
    }
  },
  
  // 测试 4: 异步组件
  async testAsyncComponent() {
    console.log('🧪 测试 4: 异步组件')
    
    try {
      const { descriptor } = store.compiler.parse(mockSFCCode, {
        filename: 'AsyncTest.vue'
      })
      
      // 创建异步组件
      const asyncComponent = createAsyncDynamicComponent(descriptor)
      
      console.log('✅ 异步组件创建成功:', asyncComponent)
      return { success: true, asyncComponent }
      
    } catch (error) {
      console.error('❌ 异步组件测试失败:', error)
      return { success: false, error: error.message }
    }
  },
  
  // 测试 5: 错误处理
  async testErrorHandling() {
    console.log('🧪 测试 5: 错误处理')
    
    const invalidSFCCode = `
    <template>
      <div>{{ undefinedVariable }}</div>
    </template>
    
    <script setup>
    // 故意的语法错误
    const invalid = 
    </script>
    `
    
    try {
      const { descriptor, errors } = store.compiler.parse(invalidSFCCode, {
        filename: 'ErrorTest.vue'
      })
      
      if (errors.length > 0) {
        console.log('✅ 正确捕获解析错误:', errors.map(e => e.message))
        return { success: true, errors: errors.map(e => e.message) }
      }
      
      // 如果解析成功，尝试创建组件（应该失败）
      try {
        await dynamicComponentManager.createComponentFromDescriptor(descriptor)
        console.log('❌ 应该失败但成功了')
        return { success: false, error: '应该失败但成功了' }
      } catch (createError) {
        console.log('✅ 正确捕获创建错误:', createError.message)
        return { success: true, createError: createError.message }
      }
      
    } catch (error) {
      console.log('✅ 正确捕获错误:', error.message)
      return { success: true, error: error.message }
    }
  },
  
  // 运行所有测试
  async runAllTests() {
    console.log('🚀 开始运行动态组件系统测试...')
    
    const results = {}
    
    results.basicCreation = await this.testBasicComponentCreation()
    results.caching = await this.testCaching()
    results.compositionAPI = await this.testCompositionAPI()
    results.asyncComponent = await this.testAsyncComponent()
    results.errorHandling = await this.testErrorHandling()
    
    // 统计结果
    const total = Object.keys(results).length
    const passed = Object.values(results).filter(r => r.success).length
    const failed = total - passed
    
    console.log(`\n📊 测试结果: ${passed}/${total} 通过, ${failed} 失败`)
    
    if (failed > 0) {
      console.log('❌ 失败的测试:')
      Object.entries(results).forEach(([name, result]) => {
        if (!result.success) {
          console.log(`  - ${name}: ${result.error}`)
        }
      })
    } else {
      console.log('🎉 所有测试都通过了!')
    }
    
    return {
      total,
      passed,
      failed,
      results
    }
  }
}

// 自动运行测试（如果在浏览器环境中）
if (typeof window !== 'undefined') {
  // 延迟运行，确保所有模块都已加载
  setTimeout(() => {
    dynamicComponentTests.runAllTests()
  }, 1000)
}

export default dynamicComponentTests
