<template>
  <div class="dynamic-test">
    <h2>动态组件测试</h2>
    <div class="props-display">
      <h3>接收到的 Props:</h3>
      <div class="prop-item">
        <strong>dataChart:</strong>
        <pre>{{ JSON.stringify(dataChart, null, 2) }}</pre>
      </div>
      <div class="prop-item">
        <strong>refList:</strong>
        <pre>{{ JSON.stringify(refList, null, 2) }}</pre>
      </div>
    </div>
    
    <div class="interactive-section">
      <h3>交互测试:</h3>
      <div class="controls">
        <button @click="counter++" class="btn">
          点击次数: {{ counter }}
        </button>
        <input v-model="message" placeholder="输入消息" class="input" />
        <p>当前消息: {{ message }}</p>
      </div>
    </div>
    
    <div class="chart-simulation" v-if="dataChart">
      <h3>图表模拟:</h3>
      <div class="chart-container">
        <div class="chart-info">
          <p>图表类型: {{ dataChart.type || '未指定' }}</p>
          <p>数据点数量: {{ dataChart.data ? dataChart.data.length : 0 }}</p>
        </div>
        <div class="chart-bars" v-if="dataChart.data">
          <div 
            v-for="(value, index) in dataChart.data" 
            :key="index"
            class="bar"
            :style="{ height: `${Math.max(value * 2, 10)}px` }"
            :title="`${dataChart.labels?.[index] || index}: ${value}`"
          >
            {{ value }}
          </div>
        </div>
      </div>
    </div>
    
    <div class="list-display" v-if="refList && refList.length">
      <h3>列表数据:</h3>
      <ul class="ref-list">
        <li v-for="(item, index) in refList" :key="index" class="list-item">
          <span v-if="typeof item === 'object'">
            {{ item.name || item.id || `Item ${index}` }}: 
            {{ item.value || JSON.stringify(item) }}
          </span>
          <span v-else>{{ item }}</span>
        </li>
      </ul>
    </div>
    
    <div class="status">
      <p class="timestamp">组件渲染时间: {{ renderTime }}</p>
      <p class="component-info">这是一个动态渲染的 Vue 组件</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 定义 props
const props = defineProps({
  dataChart: {
    type: Object,
    default: () => ({})
  },
  refList: {
    type: Array,
    default: () => []
  }
})

// 响应式数据
const counter = ref(0)
const message = ref('Hello Dynamic Component!')
const renderTime = ref('')

// 组件挂载时记录时间
onMounted(() => {
  renderTime.value = new Date().toLocaleTimeString()
  console.log('🎉 动态组件已成功渲染!', {
    dataChart: props.dataChart,
    refList: props.refList,
    renderTime: renderTime.value
  })
})
</script>

<style scoped>
.dynamic-test {
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  max-width: 800px;
  margin: 0 auto;
}

.dynamic-test h2 {
  color: #2c3e50;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.dynamic-test h3 {
  color: #34495e;
  margin-top: 25px;
  margin-bottom: 15px;
}

.props-display {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
}

.prop-item {
  margin-bottom: 15px;
}

.prop-item strong {
  color: #495057;
}

.prop-item pre {
  background: #fff;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 10px;
  margin: 5px 0;
  overflow-x: auto;
  font-size: 12px;
}

.interactive-section {
  background: #e3f2fd;
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
}

.controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
  align-self: flex-start;
}

.btn:hover {
  background: #0056b3;
}

.input {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  max-width: 300px;
}

.chart-simulation {
  background: #f1f8e9;
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
}

.chart-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.chart-info {
  background: #fff;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #c8e6c9;
}

.chart-bars {
  display: flex;
  align-items: flex-end;
  gap: 5px;
  height: 120px;
  padding: 10px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #c8e6c9;
}

.bar {
  background: linear-gradient(to top, #4caf50, #81c784);
  min-width: 30px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
  border-radius: 2px 2px 0 0;
  cursor: pointer;
  transition: opacity 0.2s;
}

.bar:hover {
  opacity: 0.8;
}

.list-display {
  background: #fff3e0;
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
}

.ref-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.list-item {
  background: #fff;
  border: 1px solid #ffcc02;
  border-radius: 4px;
  padding: 10px;
  margin: 5px 0;
  transition: background-color 0.2s;
}

.list-item:hover {
  background: #fffde7;
}

.status {
  background: #f3e5f5;
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
  text-align: center;
}

.timestamp {
  color: #7b1fa2;
  font-weight: bold;
  margin: 0 0 5px 0;
}

.component-info {
  color: #8e24aa;
  margin: 0;
  font-style: italic;
}
</style>
