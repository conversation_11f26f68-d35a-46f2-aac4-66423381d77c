name: Release

on:
  push:
    tags:
      - 'v*'

jobs:
  release:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      id-token: write

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.node-version'
          cache: pnpm
          registry-url: 'https://registry.npmjs.org'

      - run: npx changelogithub
        continue-on-error: true
        env:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}

      - name: Install Dependencies
        run: pnpm i

      - name: Publish to NPM
        run: npm i -g npm && pnpm -r publish --access public --no-git-checks
