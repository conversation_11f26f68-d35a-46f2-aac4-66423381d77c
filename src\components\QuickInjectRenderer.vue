<template>
  <div class="quick-inject-renderer">
    <div class="toolbar">
      <button @click="processAndRender" :disabled="loading" class="primary-btn">
        {{ loading ? '处理中...' : '应用注入并渲染' }}
      </button>
      <button @click="toggleCodeView" class="secondary-btn">
        {{ showCode ? '隐藏代码' : '显示代码' }}
      </button>
      <span class="status" :class="statusClass">{{ statusText }}</span>
    </div>

    <!-- 代码对比视图 -->
    <div v-if="showCode" class="code-view">
      <div class="code-panels">
        <div class="code-panel">
          <h4>原始代码</h4>
          <pre class="code-content">{{ originalCode }}</pre>
        </div>
        <div class="code-panel">
          <h4>注入后代码</h4>
          <pre class="code-content">{{ injectedCode }}</pre>
        </div>
      </div>
    </div>

    <!-- 组件渲染区域 -->
    <div class="render-area">
      <div v-if="error" class="error-message">
        <h4>❌ 渲染错误</h4>
        <pre>{{ error }}</pre>
      </div>
      
      <div v-else-if="renderedComponent" class="component-container">
        <div class="component-header">
          <h4>✅ 注入后的组件渲染结果</h4>
          <div class="props-info">
            <span>Props: dataChart ({{ typeof defaultProps.dataChart }}), refList ({{ Array.isArray(defaultProps.refList) ? 'Array' : typeof defaultProps.refList }})</span>
          </div>
        </div>
        
        <div class="component-wrapper">
          <component 
            :is="renderedComponent" 
            v-bind="defaultProps"
            @error="onRenderError"
          />
        </div>
      </div>
      
      <div v-else-if="!loading" class="empty-state">
        <p>点击 "应用注入并渲染" 按钮开始</p>
      </div>
      
      <div v-if="loading" class="loading-state">
        <div class="spinner"></div>
        <p>正在处理和渲染组件...</p>
      </div>
    </div>

    <!-- Props 编辑器 -->
    <div class="props-editor">
      <h4>Props 配置</h4>
      <div class="props-controls">
        <div class="prop-control">
          <label>dataChart (JSON):</label>
          <textarea 
            v-model="dataChartInput" 
            @change="updateProps"
            placeholder='{"type": "bar", "data": [1,2,3]}'
          ></textarea>
        </div>
        <div class="prop-control">
          <label>refList (JSON):</label>
          <textarea 
            v-model="refListInput" 
            @change="updateProps"
            placeholder='[{"id": 1, "name": "Item 1"}]'
          ></textarea>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, inject } from 'vue'
import { createComponentFromJS } from '../dynamicComponent'
import { injectRefListToSFC } from '../transform'
import { injectKeyProps } from '../types'

// 获取 store
const { store } = inject(injectKeyProps) || {}

// 响应式状态
const loading = ref(false)
const error = ref(null)
const originalCode = ref('')
const injectedCode = ref('')
const renderedComponent = ref(null)
const showCode = ref(false)

// Props 输入
const dataChartInput = ref(JSON.stringify({
  type: 'bar',
  data: [15, 25, 35, 45, 55],
  labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri']
}, null, 2))

const refListInput = ref(JSON.stringify([
  { id: 1, name: 'Task 1', completed: false },
  { id: 2, name: 'Task 2', completed: true },
  { id: 3, name: 'Task 3', completed: false }
], null, 2))

// 计算属性
const defaultProps = computed(() => {
  try {
    return {
      dataChart: JSON.parse(dataChartInput.value),
      refList: JSON.parse(refListInput.value)
    }
  } catch (e) {
    return {
      dataChart: { type: 'bar', data: [] },
      refList: []
    }
  }
})

const statusClass = computed(() => {
  if (error.value) return 'error'
  if (renderedComponent.value) return 'success'
  if (loading.value) return 'loading'
  return 'idle'
})

const statusText = computed(() => {
  if (error.value) return '渲染失败'
  if (renderedComponent.value) return '渲染成功'
  if (loading.value) return '处理中'
  return '等待处理'
})

// 方法
const processAndRender = async () => {
  const activeFile = store.activeFile
  if (!activeFile || !activeFile.filename.endsWith('.vue')) {
    error.value = '当前文件不是 Vue 组件'
    return
  }

  loading.value = true
  error.value = null
  renderedComponent.value = null

  try {
    console.log('[QuickInject] 开始处理文件:', activeFile.filename)
    
    // 1. 保存原始代码
    originalCode.value = activeFile.code
    
    // 2. 执行 props 注入
    console.log('[QuickInject] 执行 props 注入...')
    injectedCode.value = injectRefListToSFC(activeFile.code, activeFile.filename)
    
    // 3. 检查是否有变化
    const hasChanges = originalCode.value !== injectedCode.value
    console.log('[QuickInject] Props 注入结果:', hasChanges ? '有变化' : '无变化')
    
    // 4. 使用当前文件的编译结果
    if (!activeFile.compiled?.js) {
      throw new Error('当前文件未编译或编译失败')
    }

    // 5. 从编译后的 JS 创建组件
    const component = createComponentFromJS(activeFile.compiled.js, activeFile.filename)

    renderedComponent.value = component
    
  } catch (err) {
    console.error('[QuickInject] 处理失败:', err)
    error.value = err.message
  } finally {
    loading.value = false
  }
}

const toggleCodeView = () => {
  showCode.value = !showCode.value
}

const updateProps = () => {
  // Props 更新时自动重新渲染（如果组件已存在）
  if (renderedComponent.value) {
    console.log('[QuickInject] Props 已更新')
  }
}

const onRenderError = (err) => {
  console.error('[QuickInject] 组件运行时错误:', err)
  error.value = `组件运行时错误: ${err.message}`
}

// 监听文件变化
watch(() => store.activeFile, () => {
  // 文件变化时清除状态
  renderedComponent.value = null
  error.value = null
  originalCode.value = ''
  injectedCode.value = ''
}, { deep: true })

// 组件挂载时自动处理
onMounted(() => {
  if (store.activeFile?.filename.endsWith('.vue')) {
    processAndRender()
  }
})
</script>

<style scoped>
.quick-inject-renderer {
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.toolbar {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 15px;
  border-bottom: 1px solid var(--border);
  background: var(--bg);
}

.primary-btn {
  padding: 8px 16px;
  background: var(--color-branding);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
}

.primary-btn:hover:not(:disabled) {
  opacity: 0.9;
}

.primary-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.secondary-btn {
  padding: 8px 16px;
  background: var(--bg-soft);
  color: var(--text-light);
  border: 1px solid var(--border);
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
}

.secondary-btn:hover {
  background: var(--border);
}

.status {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.status.idle {
  background: var(--bg-soft);
  color: var(--text-light);
}

.status.loading {
  background: #fff3cd;
  color: #856404;
}

.status.success {
  background: #d4edda;
  color: #155724;
}

.status.error {
  background: #f8d7da;
  color: #721c24;
}

.code-view {
  border-bottom: 1px solid var(--border);
  background: var(--bg-soft);
}

.code-panels {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1px;
  background: var(--border);
}

.code-panel {
  background: var(--bg);
  padding: 10px;
}

.code-panel h4 {
  margin: 0 0 10px 0;
  font-size: 12px;
  color: var(--text-light);
}

.code-content {
  margin: 0;
  padding: 10px;
  background: var(--bg-soft);
  border-radius: 4px;
  font-size: 11px;
  font-family: var(--font-code);
  overflow-x: auto;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
}

.render-area {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.error-message {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 6px;
  padding: 15px;
  color: #721c24;
}

.error-message h4 {
  margin: 0 0 10px 0;
}

.error-message pre {
  margin: 0;
  font-size: 12px;
  white-space: pre-wrap;
}

.component-container {
  border: 1px solid var(--border);
  border-radius: 6px;
  overflow: hidden;
}

.component-header {
  padding: 10px 15px;
  background: var(--bg-soft);
  border-bottom: 1px solid var(--border);
}

.component-header h4 {
  margin: 0 0 5px 0;
  font-size: 13px;
  color: #155724;
}

.props-info {
  font-size: 11px;
  color: var(--text-light);
  font-family: var(--font-code);
}

.component-wrapper {
  padding: 20px;
  background: var(--bg);
  min-height: 200px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-light);
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--text-light);
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border);
  border-top: 3px solid var(--color-branding);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.props-editor {
  border-top: 1px solid var(--border);
  background: var(--bg-soft);
  padding: 15px;
}

.props-editor h4 {
  margin: 0 0 15px 0;
  font-size: 13px;
  color: var(--text-light);
}

.props-controls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.prop-control label {
  display: block;
  margin-bottom: 5px;
  font-size: 12px;
  font-weight: 500;
  color: var(--text-light);
}

.prop-control textarea {
  width: 100%;
  height: 80px;
  padding: 8px;
  border: 1px solid var(--border);
  border-radius: 4px;
  font-family: var(--font-code);
  font-size: 11px;
  background: var(--bg);
  color: inherit;
  resize: vertical;
}

.prop-control textarea:focus {
  outline: none;
  border-color: var(--color-branding);
}
</style>
