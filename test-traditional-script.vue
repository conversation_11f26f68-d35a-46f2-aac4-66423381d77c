<template>
  <div style="padding: 20px; border: 2px solid #e74c3c; border-radius: 8px; margin: 10px;">
    <h1>传统Script语法 Props注入测试</h1>
    
    <div style="background: #fdf2f2; padding: 15px; border-radius: 5px; margin: 15px 0;">
      <h3>Props 信息</h3>
      <pre style="background: white; padding: 10px; border-radius: 3px;">{{ JSON.stringify($props, null, 2) }}</pre>
    </div>
    
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
      <div style="background: #e8f4fd; padding: 15px; border-radius: 5px;">
        <h4>DataChart</h4>
        <p><strong>值:</strong> {{ dataChart || 'undefined' }}</p>
        <p><strong>类型:</strong> {{ typeof dataChart }}</p>
      </div>
      
      <div style="background: #fff2e8; padding: 15px; border-radius: 5px;">
        <h4>RefList</h4>
        <p><strong>值:</strong> {{ refList || 'undefined' }}</p>
        <p><strong>类型:</strong> {{ typeof refList }}</p>
        <p><strong>是数组:</strong> {{ Array.isArray(refList) }}</p>
      </div>
    </div>
    
    <div style="margin-top: 15px;">
      <button @click="testTraditional" style="background: #e74c3c; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
        测试传统语法Props
      </button>
    </div>
    
    <div v-if="results.length > 0" style="margin-top: 15px; background: #f9f9f9; padding: 15px; border-radius: 5px;">
      <h4>测试结果:</h4>
      <ul>
        <li v-for="(result, index) in results" :key="index" :style="{ color: result.includes('✅') ? 'green' : 'red' }">
          {{ result }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TraditionalScriptTest',
  data() {
    return {
      results: []
    }
  },
  methods: {
    testTraditional() {
      this.results = []
      
      // 测试 dataChart
      if (this.dataChart !== undefined) {
        this.results.push(`✅ dataChart 可访问，类型: ${typeof this.dataChart}`)
      } else {
        this.results.push('❌ dataChart 未定义')
      }
      
      // 测试 refList
      if (this.refList !== undefined) {
        this.results.push(`✅ refList 可访问，类型: ${typeof this.refList}`)
        if (Array.isArray(this.refList)) {
          this.results.push('✅ refList 是数组类型')
        }
      } else {
        this.results.push('❌ refList 未定义')
      }
      
      console.log('传统语法测试结果:', this.results)
    }
  },
  mounted() {
    console.log('传统Script组件加载')
    console.log('dataChart:', this.dataChart)
    console.log('refList:', this.refList)
    console.log('所有props:', this.$props)
  }
}
</script>

<style scoped>
h1 {
  color: #2c3e50;
  text-align: center;
}

h3, h4 {
  margin-top: 0;
  color: #34495e;
}

button:hover {
  background: #c0392b !important;
}

pre {
  font-size: 12px;
  max-height: 200px;
}
</style>
