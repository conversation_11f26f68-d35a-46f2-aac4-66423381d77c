import type { Component } from 'vue'

/**
 * 简化的动态组件管理器
 * 直接从编译后的 JavaScript 代码创建 Vue 组件
 */
export class DynamicComponentManager {
  private componentCache = new Map<string, Component>()
  
  /**
   * 从编译后的 JavaScript 代码创建组件
   */
  createComponentFromCompiledJS(compiledJS: string, filename: string): Component {
    // 检查缓存
    if (this.componentCache.has(filename)) {
      return this.componentCache.get(filename)!
    }
    
    try {
      // 执行编译后的代码
      const component = this.executeCompiledCode(compiledJS)
      
      // 缓存组件
      this.componentCache.set(filename, component)
      
      console.log(`[DynamicComponent] Created component for ${filename}`)
      return component
      
    } catch (error) {
      console.error(`[DynamicComponent] Failed to create component for ${filename}:`, error)
      throw error
    }
  }
  
  /**
   * 执行编译后的 JavaScript 代码并提取组件
   */
  private executeCompiledCode(compiledJS: string): Component {
    try {
      // 创建一个安全的执行环境
      const moduleExports = { default: null }
      const moduleScope = {
        exports: moduleExports,
        module: { exports: moduleExports },
        Vue: (window as any).Vue,
        console,
      }
      
      // 执行编译后的代码
      const func = new Function(
        'exports', 'module', 'Vue', 'console',
        `
        ${compiledJS}
        return typeof __sfc__ !== 'undefined' ? __sfc__ : (module.exports.default || module.exports);
        `
      )
      
      const component = func(
        moduleScope.exports,
        moduleScope.module,
        moduleScope.Vue,
        moduleScope.console
      )
      
      if (!component) {
        throw new Error('Failed to extract component from compiled code')
      }
      
      return component
      
    } catch (error) {
      console.error('[DynamicComponent] Error executing compiled code:', error)
      throw new Error(`Failed to create component: ${(error as Error).message}`)
    }
  }
  
  /**
   * 清除缓存
   */
  clearCache(filename?: string) {
    if (filename) {
      this.componentCache.delete(filename)
      console.log(`[DynamicComponent] Cleared cache for ${filename}`)
    } else {
      this.componentCache.clear()
      console.log('[DynamicComponent] Cleared all cache')
    }
  }
  
  /**
   * 获取缓存的组件
   */
  getCachedComponent(filename: string): Component | undefined {
    return this.componentCache.get(filename)
  }
  
  /**
   * 获取所有缓存的组件
   */
  getAllCachedComponents(): Map<string, Component> {
    return new Map(this.componentCache)
  }
}

// 创建全局实例
export const dynamicComponentManager = new DynamicComponentManager()

/**
 * 辅助函数：从编译后的 JS 代码创建组件
 */
export function createComponentFromJS(compiledJS: string, filename = 'component.vue'): Component {
  return dynamicComponentManager.createComponentFromCompiledJS(compiledJS, filename)
}
