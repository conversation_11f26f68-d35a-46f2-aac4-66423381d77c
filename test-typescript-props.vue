<template>
  <div style="padding: 20px; border: 2px solid #42b883; border-radius: 8px; margin: 10px;">
    <h1>TypeScript Props 注入测试</h1>
    
    <div style="background: #f0f8f0; padding: 15px; border-radius: 5px; margin: 15px 0;">
      <h3>Props 信息</h3>
      <pre style="background: white; padding: 10px; border-radius: 3px; overflow: auto;">{{ JSON.stringify($props, null, 2) }}</pre>
    </div>
    
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
      <div style="background: #e8f4fd; padding: 15px; border-radius: 5px;">
        <h4>DataChart</h4>
        <p><strong>值:</strong> {{ dataChart || 'undefined' }}</p>
        <p><strong>类型:</strong> {{ typeof dataChart }}</p>
      </div>
      
      <div style="background: #fff2e8; padding: 15px; border-radius: 5px;">
        <h4>RefList</h4>
        <p><strong>值:</strong> {{ refList || 'undefined' }}</p>
        <p><strong>类型:</strong> {{ typeof refList }}</p>
        <p><strong>是数组:</strong> {{ Array.isArray(refList) }}</p>
      </div>
    </div>
    
    <div style="margin-top: 15px;">
      <button @click="testTypeScript" style="background: #42b883; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
        测试TypeScript类型
      </button>
    </div>
    
    <div v-if="typeTestResults.length > 0" style="margin-top: 15px; background: #f9f9f9; padding: 15px; border-radius: 5px;">
      <h4>TypeScript 测试结果:</h4>
      <ul>
        <li v-for="(result, index) in typeTestResults" :key="index" :style="{ color: result.includes('✅') ? 'green' : 'red' }">
          {{ result }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 这里应该自动注入 dataChart 和 refList props
// dataChart?: Object
// refList?: Array<any> | Object

const typeTestResults = ref<string[]>([])

const testTypeScript = () => {
  typeTestResults.value = []
  
  // 测试 dataChart 类型
  try {
    if (dataChart !== undefined) {
      typeTestResults.value.push(`✅ dataChart 可访问，类型: ${typeof dataChart}`)
      
      // 尝试作为对象使用
      if (typeof dataChart === 'object') {
        typeTestResults.value.push('✅ dataChart 是对象类型')
      }
    } else {
      typeTestResults.value.push('❌ dataChart 未定义')
    }
  } catch (error) {
    typeTestResults.value.push(`❌ dataChart 访问错误: ${error}`)
  }
  
  // 测试 refList 类型
  try {
    if (refList !== undefined) {
      typeTestResults.value.push(`✅ refList 可访问，类型: ${typeof refList}`)
      
      // 尝试作为数组使用
      if (Array.isArray(refList)) {
        typeTestResults.value.push('✅ refList 是数组类型')
        typeTestResults.value.push(`✅ refList 长度: ${refList.length}`)
      } else if (typeof refList === 'object') {
        typeTestResults.value.push('✅ refList 是对象类型')
      }
    } else {
      typeTestResults.value.push('❌ refList 未定义')
    }
  } catch (error) {
    typeTestResults.value.push(`❌ refList 访问错误: ${error}`)
  }
  
  console.log('TypeScript 测试完成:', typeTestResults.value)
}

// 组件加载时自动测试
console.log('TypeScript 组件加载')
console.log('dataChart:', dataChart)
console.log('refList:', refList)
</script>

<style scoped>
h1 {
  color: #2c3e50;
  text-align: center;
}

h3, h4 {
  margin-top: 0;
  color: #34495e;
}

button:hover {
  background: #369870 !important;
}

pre {
  font-size: 12px;
  max-height: 200px;
}
</style>
