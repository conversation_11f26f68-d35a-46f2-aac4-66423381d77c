<template>
  <div>
    <h1>TypeScript defineProps 测试</h1>
    <p>Title: {{ title }}</p>
    <p>Count: {{ count }}</p>
    <p>RefList: {{ refList }}</p>
    <p>All Props: {{ JSON.stringify($props) }}</p>
  </div>
</template>

<script setup lang="ts">
// 测试TypeScript defineProps形式的refList注入
interface Props {
  title: string
  count?: number
}

const props = defineProps<Props>()

console.log('Props received:', props)
</script>

<style scoped>
div {
  padding: 20px;
  border: 2px solid #42b883;
  border-radius: 8px;
  margin: 10px;
}

h1 {
  color: #42b883;
}

p {
  margin: 8px 0;
  padding: 8px;
  background: #f0f8f0;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}
</style>
