{"name": "@vue/repl", "version": "4.6.3", "description": "Vue component for editing Vue components", "packageManager": "pnpm@10.14.0", "type": "module", "main": "dist/ssr-stub.js", "module": "dist/vue-repl.js", "files": ["dist"], "types": "dist/vue-repl.d.ts", "exports": {".": {"types": "./dist/vue-repl.d.ts", "import": "./dist/vue-repl.js", "require": "./dist/ssr-stub.js"}, "./monaco-editor": {"types": "./dist/monaco-editor.d.ts", "import": "./dist/monaco-editor.js", "require": null}, "./codemirror-editor": {"types": "./dist/codemirror-editor.d.ts", "import": "./dist/codemirror-editor.js", "require": null}, "./core": {"types": "./dist/core.d.ts", "import": "./dist/core.js", "require": null}, "./package.json": "./package.json", "./style.css": "./dist/vue-repl.css", "./dist/style.css": "./dist/vue-repl.css"}, "typesVersions": {"*": {"*": ["./dist/*", "./*"]}}, "publishConfig": {"tag": "latest"}, "scripts": {"dev": "vite", "build": "vite build", "build-preview": "vite build -c vite.preview.config.ts", "format": "prettier --write .", "lint": "eslint .", "typecheck": "vue-tsc --noEmit", "release": "bumpp --all", "version": "conventional-changelog -p angular -i CHANGELOG.md -s -r 0", "prepublishOnly": "npm run build"}, "simple-git-hooks": {"pre-commit": "pnpm exec lint-staged --concurrent false"}, "lint-staged": {"*": ["prettier --write --cache --ignore-unknown"]}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/repl.git"}, "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/repl/issues"}, "homepage": "https://github.com/vuejs/repl#readme", "devDependencies": {"@babel/standalone": "^7.28.2", "@babel/types": "^7.28.2", "@eslint/js": "^9.32.0", "@jridgewell/gen-mapping": "^0.3.12", "@jridgewell/trace-mapping": "^0.3.29", "@rollup/plugin-replace": "^6.0.2", "@shikijs/monaco": "^3.9.2", "@types/babel__standalone": "^7.1.9", "@types/codemirror": "^5.60.16", "@types/hash-sum": "^1.0.2", "@types/node": "^24.2.0", "@vitejs/plugin-vue": "^6.0.1", "@volar/jsdelivr": "~2.4.11", "@volar/monaco": "~2.4.11", "@vue/babel-plugin-jsx": "^1.4.0", "@vue/language-service": "~2.2.2", "assert": "^2.1.0", "bumpp": "^10.2.2", "codemirror": "^5.65.18", "conventional-changelog-cli": "^5.0.0", "eslint": "^9.32.0", "eslint-plugin-vue": "^10.4.0", "fflate": "^0.8.2", "hash-sum": "^2.0.0", "lint-staged": "^16.1.4", "monaco-editor-core": "^0.52.2", "prettier": "^3.6.2", "shiki": "^3.9.2", "simple-git-hooks": "^2.13.1", "source-map-js": "^1.2.1", "sucrase": "^3.35.0", "typescript": "^5.9.2", "typescript-eslint": "^8.39.0", "vite": "^7.0.6", "vite-plugin-dts": "^4.5.4", "vite-plugin-vue-devtools": "^8.0.0", "vscode-uri": "^3.1.0", "vue": "^3.5.18", "vue-tsc": "~3.0.5"}}