<template>
  <div class="inject-test">
    <h2>Props 注入测试组件</h2>
    
    <div class="test-section">
      <h3>🔍 Props 检测</h3>
      <div class="props-status">
        <div class="prop-check">
          <span class="prop-name">dataChart:</span>
          <span :class="dataChartStatus.class">{{ dataChartStatus.text }}</span>
        </div>
        <div class="prop-check">
          <span class="prop-name">refList:</span>
          <span :class="refListStatus.class">{{ refListStatus.text }}</span>
        </div>
      </div>
    </div>

    <div class="test-section" v-if="hasDataChart">
      <h3>📊 DataChart 数据</h3>
      <div class="data-display">
        <div class="data-info">
          <p><strong>类型:</strong> {{ dataChart.type || '未指定' }}</p>
          <p><strong>数据点:</strong> {{ dataChart.data ? dataChart.data.length : 0 }}</p>
        </div>
        <div class="chart-preview" v-if="dataChart.data && dataChart.data.length">
          <div class="bars">
            <div 
              v-for="(value, index) in dataChart.data.slice(0, 10)" 
              :key="index"
              class="bar"
              :style="{ height: `${Math.max(value * 2, 5)}px` }"
              :title="`${dataChart.labels?.[index] || index}: ${value}`"
            >
              {{ value }}
            </div>
          </div>
        </div>
        <div class="raw-data">
          <strong>原始数据:</strong>
          <pre>{{ JSON.stringify(dataChart, null, 2) }}</pre>
        </div>
      </div>
    </div>

    <div class="test-section" v-if="hasRefList">
      <h3>📋 RefList 数据</h3>
      <div class="list-display">
        <div class="list-info">
          <p><strong>类型:</strong> {{ Array.isArray(refList) ? 'Array' : typeof refList }}</p>
          <p><strong>长度:</strong> {{ Array.isArray(refList) ? refList.length : '不是数组' }}</p>
        </div>
        <div class="list-items" v-if="Array.isArray(refList)">
          <div 
            v-for="(item, index) in refList.slice(0, 5)" 
            :key="index"
            class="list-item"
          >
            <span class="item-index">#{{ index + 1 }}</span>
            <span class="item-content">
              <template v-if="typeof item === 'object' && item !== null">
                {{ item.name || item.title || item.id || `Object ${index}` }}
                <small v-if="item.value !== undefined"> ({{ item.value }})</small>
              </template>
              <template v-else>
                {{ item }}
              </template>
            </span>
          </div>
          <div v-if="refList.length > 5" class="more-items">
            ... 还有 {{ refList.length - 5 }} 项
          </div>
        </div>
        <div class="raw-data">
          <strong>原始数据:</strong>
          <pre>{{ JSON.stringify(refList, null, 2) }}</pre>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>🎮 交互测试</h3>
      <div class="interactive-controls">
        <div class="control-group">
          <button @click="counter++" class="test-btn">
            点击计数: {{ counter }}
          </button>
          <button @click="resetCounter" class="test-btn secondary">
            重置
          </button>
        </div>
        <div class="control-group">
          <input 
            v-model="message" 
            placeholder="输入测试消息"
            class="test-input"
          />
          <p class="message-display">消息: {{ message }}</p>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>ℹ️ 组件信息</h3>
      <div class="component-info">
        <p><strong>渲染时间:</strong> {{ renderTime }}</p>
        <p><strong>Props 注入:</strong> {{ propsInjected ? '✅ 成功' : '❌ 失败' }}</p>
        <p><strong>组件状态:</strong> 正常运行</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 定义 props - 这里可能会被自动注入
// const props = defineProps({
//   dataChart: Object,
//   refList: Array
// })

// 响应式数据
const counter = ref(0)
const message = ref('Hello Props Injection!')
const renderTime = ref('')

// 计算属性
const hasDataChart = computed(() => {
  return typeof dataChart !== 'undefined' && dataChart !== null
})

const hasRefList = computed(() => {
  return typeof refList !== 'undefined' && refList !== null
})

const dataChartStatus = computed(() => {
  if (typeof dataChart === 'undefined') {
    return { class: 'status-error', text: '❌ 未定义' }
  }
  if (dataChart === null) {
    return { class: 'status-warning', text: '⚠️ 为 null' }
  }
  if (typeof dataChart === 'object') {
    return { class: 'status-success', text: '✅ 已注入 (Object)' }
  }
  return { class: 'status-warning', text: `⚠️ 类型: ${typeof dataChart}` }
})

const refListStatus = computed(() => {
  if (typeof refList === 'undefined') {
    return { class: 'status-error', text: '❌ 未定义' }
  }
  if (refList === null) {
    return { class: 'status-warning', text: '⚠️ 为 null' }
  }
  if (Array.isArray(refList)) {
    return { class: 'status-success', text: `✅ 已注入 (Array[${refList.length}])` }
  }
  if (typeof refList === 'object') {
    return { class: 'status-success', text: '✅ 已注入 (Object)' }
  }
  return { class: 'status-warning', text: `⚠️ 类型: ${typeof refList}` }
})

const propsInjected = computed(() => {
  return hasDataChart.value || hasRefList.value
})

// 方法
const resetCounter = () => {
  counter.value = 0
}

// 生命周期
onMounted(() => {
  renderTime.value = new Date().toLocaleTimeString()
  
  // 输出调试信息
  console.log('🧪 [InjectTest] 组件已挂载')
  console.log('📊 dataChart:', typeof dataChart !== 'undefined' ? dataChart : '未定义')
  console.log('📋 refList:', typeof refList !== 'undefined' ? refList : '未定义')
  console.log('⏰ 渲染时间:', renderTime.value)
})
</script>

<style scoped>
.inject-test {
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

.inject-test h2 {
  color: #2c3e50;
  border-bottom: 3px solid #3498db;
  padding-bottom: 10px;
  margin-bottom: 25px;
}

.inject-test h3 {
  color: #34495e;
  margin: 25px 0 15px 0;
  font-size: 18px;
}

.test-section {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.props-status {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.prop-check {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: white;
  border-radius: 5px;
  border: 1px solid #e9ecef;
}

.prop-name {
  font-weight: bold;
  min-width: 80px;
  color: #495057;
}

.status-success {
  color: #155724;
  background: #d4edda;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
}

.status-warning {
  color: #856404;
  background: #fff3cd;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
}

.status-error {
  color: #721c24;
  background: #f8d7da;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
}

.data-display, .list-display {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.data-info, .list-info {
  background: white;
  padding: 15px;
  border-radius: 5px;
  border: 1px solid #e9ecef;
}

.chart-preview {
  background: white;
  padding: 15px;
  border-radius: 5px;
  border: 1px solid #e9ecef;
}

.bars {
  display: flex;
  align-items: flex-end;
  gap: 4px;
  height: 100px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}

.bar {
  background: linear-gradient(to top, #007bff, #66b3ff);
  min-width: 25px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  color: white;
  font-size: 11px;
  font-weight: bold;
  border-radius: 2px 2px 0 0;
  cursor: pointer;
  transition: opacity 0.2s;
}

.bar:hover {
  opacity: 0.8;
}

.list-items {
  background: white;
  border-radius: 5px;
  border: 1px solid #e9ecef;
  overflow: hidden;
}

.list-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 15px;
  border-bottom: 1px solid #f1f3f4;
}

.list-item:last-child {
  border-bottom: none;
}

.item-index {
  background: #007bff;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: bold;
  min-width: 30px;
  text-align: center;
}

.item-content {
  flex: 1;
}

.item-content small {
  color: #6c757d;
}

.more-items {
  padding: 10px 15px;
  color: #6c757d;
  font-style: italic;
  text-align: center;
  background: #f8f9fa;
}

.raw-data {
  background: white;
  padding: 15px;
  border-radius: 5px;
  border: 1px solid #e9ecef;
}

.raw-data pre {
  margin: 5px 0 0 0;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  max-height: 150px;
  overflow-y: auto;
}

.interactive-controls {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.test-btn {
  padding: 10px 15px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.test-btn:hover {
  background: #0056b3;
}

.test-btn.secondary {
  background: #6c757d;
}

.test-btn.secondary:hover {
  background: #545b62;
}

.test-input {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  min-width: 200px;
}

.test-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.message-display {
  margin: 5px 0;
  color: #495057;
  font-style: italic;
}

.component-info {
  background: white;
  padding: 15px;
  border-radius: 5px;
  border: 1px solid #e9ecef;
}

.component-info p {
  margin: 8px 0;
  color: #495057;
}
</style>
