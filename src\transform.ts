import type { File, Store } from './store'
import type {
  BindingMetadata,
  CompilerOptions,
  SFCDescriptor,
} from 'vue/compiler-sfc'
import { type Transform, transform } from 'sucrase'
import hashId from 'hash-sum'
import { getSourceMap, toVisualizer, trimAnalyzedBindings } from './sourcemap'

export const COMP_IDENTIFIER = `__sfc__`

// 在SFC源代码中注入默认props的函数
export function injectRefListToSFC(code: string, filename: string): string {
  // 检查是否已经正确定义了props（而不是仅仅引用）
  const hasProperDataChart = code.includes('defineProps') && code.includes('dataChart') ||
                             (code.includes('props:') && code.includes('dataChart'))
  const hasProperRefList = code.includes('defineProps') && code.includes('refList') ||
                          (code.includes('props:') && code.includes('refList'))

  if (hasProperDataChart && hasProperRefList) {
    console.log(`[Props Injection] ${filename} already has properly defined dataChart and refList props, skipping`)
    return code
  }

  // 生成需要注入的props
  const generatePropsToInject = (isTS: boolean) => {
    if (isTS) {
      const props = []
      if (!hasProperDataChart) props.push('dataChart?: Object')
      if (!hasProperRefList) props.push('refList?: Array<any> | Object')
      return props.join(', ')
    } else {
      const props = []
      if (!hasProperDataChart) props.push('dataChart: Object')
      if (!hasProperRefList) props.push('refList: [Array, Object]')
      return props.join(', ')
    }
  }

  // 检查是否有<script setup>
  const scriptSetupMatch = code.match(/<script\s+setup[^>]*>([\s\S]*?)<\/script>/i)
  if (scriptSetupMatch) {
    const scriptContent = scriptSetupMatch[1]
    const isTS = scriptSetupMatch[0].includes('lang="ts"') || scriptSetupMatch[0].includes("lang='ts'")

    // 检查是否已经有defineProps
    if (scriptContent.includes('defineProps')) {
      // 如果有defineProps，修改它来包含新的props
      const modifiedScript = scriptContent.replace(
        /defineProps\s*<\s*\{([^}]*)\}\s*>\s*\(\s*\)/g,
        (_, propsContent) => {
          const trimmedContent = propsContent.trim()
          const propsToInject = generatePropsToInject(true)
          if (!propsToInject) return _
          const separator = trimmedContent ? ', ' : ''
          return `defineProps<{${trimmedContent}${separator}${propsToInject} }>()`
        }
      ).replace(
        /defineProps\s*\(\s*\{([^}]*)\}\s*\)/g,
        (_, propsContent) => {
          const trimmedContent = propsContent.trim()
          const propsToInject = generatePropsToInject(isTS)
          if (!propsToInject) return _
          const separator = trimmedContent ? ', ' : ''
          return `defineProps({${trimmedContent}${separator}${propsToInject} })`
        }
      )

      if (modifiedScript !== scriptContent) {
        const newCode = code.replace(scriptSetupMatch[0],
          scriptSetupMatch[0].replace(scriptContent, modifiedScript))
        console.log(`[Props Injection] Modified existing defineProps in ${filename}`)
        return newCode
      }
    } else {
      // 如果没有defineProps，添加一个
      const propsToInject = generatePropsToInject(false)
      if (propsToInject) {
        const newScriptContent = `const props = defineProps({ ${propsToInject} })\n\n${scriptContent}`
        const newCode = code.replace(scriptSetupMatch[0],
          scriptSetupMatch[0].replace(scriptContent, newScriptContent))
        console.log(`[Props Injection] Added defineProps to ${filename}`)
        return newCode
      }
    }
  }

  // 检查是否有普通的<script>
  const scriptMatch = code.match(/<script[^>]*>([\s\S]*?)<\/script>/i)
  if (scriptMatch && !scriptMatch[0].includes('setup')) {
    const scriptContent = scriptMatch[1]

    // 生成需要注入的props对象
    const generatePropsObject = () => {
      const props = []
      if (!hasProperDataChart) props.push('dataChart: Object')
      if (!hasProperRefList) props.push('refList: [Array, Object]')
      return props.join(', ')
    }

    // 查找export default
    if (scriptContent.includes('export default')) {
      // 使用更精确的方法来解析export default对象
      const exportDefaultMatch = scriptContent.match(/export\s+default\s*\{/)
      if (exportDefaultMatch) {
        const startIndex = exportDefaultMatch.index! + exportDefaultMatch[0].length - 1
        let braceCount = 0
        let endIndex = startIndex

        // 找到匹配的结束大括号
        for (let i = startIndex; i < scriptContent.length; i++) {
          if (scriptContent[i] === '{') braceCount++
          else if (scriptContent[i] === '}') braceCount--

          if (braceCount === 0) {
            endIndex = i
            break
          }
        }

        const componentContent = scriptContent.slice(startIndex + 1, endIndex)
        let modifiedContent = componentContent

        // 检查是否已经有props
        if (componentContent.includes('props:')) {
          // 更新现有的props
          modifiedContent = componentContent.replace(
            /props\s*:\s*\{[^}]*\}/g,
            (propsMatch) => {
              const propsContent = propsMatch.match(/\{([^}]*)\}/)?.[1] || ''
              const trimmedContent = propsContent.trim()
              const propsToInject = generatePropsObject()
              if (!propsToInject) return propsMatch
              const separator = trimmedContent ? ', ' : ''
              return `props: {${trimmedContent}${separator}${propsToInject}}`
            }
          )
        } else {
          // 添加props到组件选项的开头
          const propsToInject = generatePropsObject()
          if (propsToInject) {
            const trimmedContent = componentContent.trim()
            const separator = trimmedContent ? ', ' : ''
            modifiedContent = `props: { ${propsToInject} }${separator}${trimmedContent}`
          }
        }

        if (modifiedContent !== componentContent) {
          const modifiedScript = scriptContent.slice(0, startIndex + 1) +
                                modifiedContent +
                                scriptContent.slice(endIndex)
          const newCode = code.replace(scriptMatch[0],
            scriptMatch[0].replace(scriptContent, modifiedScript))
          console.log(`[Props Injection] Modified export default in ${filename}`)
          return newCode
        }
      }
    }
  }

  console.log(`[Props Injection] No suitable location found in ${filename}`)
  return code
}

const REGEX_JS = /\.[jt]sx?$/
function testTs(filename: string | undefined | null) {
  return !!(filename && /(\.|\b)tsx?$/.test(filename))
}
function testJsx(filename: string | undefined | null) {
  return !!(filename && /(\.|\b)[jt]sx$/.test(filename))
}

function transformTS(src: string, isJSX?: boolean) {
  return transform(src, {
    transforms: ['typescript', ...(isJSX ? (['jsx'] as Transform[]) : [])],
    jsxRuntime: 'preserve',
  }).code
}

export async function compileFile(
  store: Store,
  { filename, code, compiled }: File,
): Promise<(string | Error)[]> {
  console.log('compileFile-compiled', compiled)
  console.log('store.sfcOptions', store);
  if (!code.trim()) {
    return []
  }

  if (filename.endsWith('.css')) {
    compiled.css = code
    return []
  }

  if (REGEX_JS.test(filename)) {
    const isJSX = testJsx(filename)
    if (testTs(filename)) {
      code = transformTS(code, isJSX)
    }
    if (isJSX) {
      code = await import('./jsx').then(({ transformJSX }) =>
        transformJSX(code),
      )
    }
    compiled.js = compiled.ssr = code
    return []
  }

  if (filename.endsWith('.json')) {
    let parsed
    try {
      parsed = JSON.parse(code)
    } catch (err: any) {
      console.error(`Error parsing ${filename}`, err.message)
      return [err.message]
    }
    compiled.js = compiled.ssr = `export default ${JSON.stringify(parsed)}`
    return []
  }

  if (!filename.endsWith('.vue')) {
    return []
  }

  const id = hashId(filename)

  const originalCode = code
  code = injectRefListToSFC(code, filename)
  console.log(code,'code');
  
  if (code !== originalCode) {
    console.log(`[Props Injection] Successfully modified ${filename}`)
  } else {
    console.log(`[Props Injection] No changes made to ${filename}`)
  }

  const { errors, descriptor } = store.compiler.parse(code, {
    filename,
    sourceMap: true,
    templateParseOptions: store.sfcOptions?.template?.compilerOptions,
  })

  console.log(descriptor,'descriptor');
  
  

  if (errors.length) {
    return errors
  }

  const styleLangs = descriptor.styles.map((s) => s.lang).filter(Boolean)
  const templateLang = descriptor.template?.lang
  if (styleLangs.length && templateLang) {
    return [
      `lang="${styleLangs.join(
        ',',
      )}" pre-processors for <style> and lang="${templateLang}" ` +
        `for <template> are currently not supported.`,
    ]
  } else if (styleLangs.length) {
    return [
      `lang="${styleLangs.join(
        ',',
      )}" pre-processors for <style> are currently not supported.`,
    ]
  } else if (templateLang) {
    return [
      `lang="${templateLang}" pre-processors for ` +
        `<template> are currently not supported.`,
    ]
  }

  const scriptLang = descriptor.script?.lang || descriptor.scriptSetup?.lang
  const isTS = testTs(scriptLang)
  const isJSX = testJsx(scriptLang)

  if (scriptLang && scriptLang !== 'js' && !isTS && !isJSX) {
    return [`Unsupported lang "${scriptLang}" in <script> blocks.`]
  }

  const hasScoped = descriptor.styles.some((s) => s.scoped)
  let clientCode = ''
  let ssrCode = ''
  let ssrScript = ''
  let clientScriptMap: any
  let clientTemplateMap: any
  let ssrScriptMap: any
  let ssrTemplateMap: any

  const appendSharedCode = (code: string) => {
    clientCode += code
    ssrCode += code
  }

  let clientScript: string
  let bindings: BindingMetadata | undefined
  try {
    const res = await doCompileScript(store, descriptor, id, false, isTS, isJSX)
    clientScript = res.code
    bindings = res.bindings
    clientScriptMap = res.map
  } catch (e: any) {
    return [e.stack.split('\n').slice(0, 12).join('\n')]
  }

  clientCode += clientScript

  // script ssr needs to be performed if :
  // 1.using <script setup> where the render fn is inlined.
  // 2.using cssVars, as it do not need to be injected during SSR.
  if (descriptor.scriptSetup || descriptor.cssVars.length > 0) {
    try {
      const ssrScriptResult = await doCompileScript(
        store,
        descriptor,
        id,
        true,
        isTS,
        isJSX,
      )
      ssrScript = ssrScriptResult.code
      ssrCode += ssrScript
      ssrScriptMap = ssrScriptResult.map
    } catch (e) {
      ssrCode = `/* SSR compile error: ${e} */`
    }
  } else {
    // the script result will be identical.
    ssrCode += clientScript
  }

  // template
  // only need dedicated compilation if not using <script setup>
  if (
    descriptor.template &&
    (!descriptor.scriptSetup ||
      store.sfcOptions?.script?.inlineTemplate === false)
  ) {
    const clientTemplateResult = await doCompileTemplate(
      store,
      descriptor,
      id,
      bindings,
      false,
      isTS,
      isJSX,
    )
    if (clientTemplateResult.errors.length) {
      return clientTemplateResult.errors
    }
    clientCode += `;${clientTemplateResult.code}`
    clientTemplateMap = clientTemplateResult.map

    const ssrTemplateResult = await doCompileTemplate(
      store,
      descriptor,
      id,
      bindings,
      true,
      isTS,
      isJSX,
    )
    if (ssrTemplateResult.code) {
      // ssr compile failure is fine
      ssrCode += `;${ssrTemplateResult.code}`
      ssrTemplateMap = ssrTemplateResult.map
    } else {
      ssrCode = `/* SSR compile error: ${ssrTemplateResult.errors[0]} */`
    }
  }

  if (isJSX) {
    const { transformJSX } = await import('./jsx')
    clientCode &&= transformJSX(clientCode)
    ssrCode &&= transformJSX(ssrCode)
  }

  if (hasScoped) {
    appendSharedCode(
      `\n${COMP_IDENTIFIER}.__scopeId = ${JSON.stringify(`data-v-${id}`)}`,
    )
  }

  // styles
  const ceFilter = store.sfcOptions.script?.customElement || /\.ce\.vue$/
  function isCustomElement(filters: typeof ceFilter): boolean {
    if (typeof filters === 'boolean') {
      return filters
    }
    if (typeof filters === 'function') {
      return filters(filename)
    }
    return filters.test(filename)
  }
  let isCE = isCustomElement(ceFilter)

  let css = ''
  let styles: string[] = []
  for (const style of descriptor.styles) {
    if (style.module) {
      return [`<style module> is not supported in the playground.`]
    }

    const styleResult = await store.compiler.compileStyleAsync({
      ...store.sfcOptions?.style,
      source: style.content,
      filename,
      id,
      scoped: style.scoped,
      modules: !!style.module,
    })
    if (styleResult.errors.length) {
      // postcss uses pathToFileURL which isn't polyfilled in the browser
      // ignore these errors for now
      if (!styleResult.errors[0].message.includes('pathToFileURL')) {
        store.errors = styleResult.errors
      }
      // proceed even if css compile errors
    } else {
      isCE ? styles.push(styleResult.code) : (css += styleResult.code + '\n')
    }
  }
  if (css) {
    compiled.css = css.trim()
  } else {
    compiled.css = isCE
      ? (compiled.css =
          '/* The component style of the custom element will be compiled into the component object */')
      : '/* No <style> tags present */'
  }

  if (clientCode || ssrCode) {
    const ceStyles = isCE
      ? `\n${COMP_IDENTIFIER}.styles = ${JSON.stringify(styles)}`
      : ''
    appendSharedCode(
      `\n${COMP_IDENTIFIER}.__file = ${JSON.stringify(filename)}` +
        ceStyles +
        `\nexport default ${COMP_IDENTIFIER}`,
    )
    compiled.js = clientCode.trimStart()
    compiled.ssr = ssrCode.trimStart()
    compiled.clientMap = toVisualizer(
      trimAnalyzedBindings(compiled.js),
      getSourceMap(filename, clientScript, clientScriptMap, clientTemplateMap),
    )
    compiled.ssrMap = toVisualizer(
      trimAnalyzedBindings(compiled.ssr),
      getSourceMap(
        filename,
        ssrScript || clientScript,
        ssrScriptMap,
        ssrTemplateMap,
      ),
    )
  }

  return []
}

// 添加默认refList到props的辅助函数
function injectRefListToProps(code: string, isTS: boolean): string {
  // 检查是否已经有refList prop
  if (code.includes('refList')) {
    console.log('[RefList Injection] RefList already exists, skipping injection')
    return code
  }

  let modifiedCode = code
  let injected = false

  // 处理 defineProps<interface>() 形式
  if (isTS && modifiedCode.includes('defineProps<')) {
    modifiedCode = modifiedCode.replace(
      /defineProps\s*<\s*\{([^}]*)\}\s*>\s*\(\s*\)/g,
      (_, propsContent) => {
        const trimmedContent = propsContent.trim()
        const separator = trimmedContent ? ',' : ''
        injected = true
        return `defineProps<{${trimmedContent}${separator} refList?: any[] }>()`
      }
    )
  }
  // 处理 defineProps({...}) 形式
  else if (modifiedCode.includes('defineProps(')) {
    modifiedCode = modifiedCode.replace(
      /defineProps\s*\(\s*\{([^}]*)\}\s*\)/g,
      (_, propsContent) => {
        const trimmedContent = propsContent.trim()
        const separator = trimmedContent ? ',' : ''
        injected = true
        return `defineProps({${trimmedContent}${separator} refList: { type: Array, default: () => [] } })`
      }
    )
  }
  // 处理传统的 props: {...} 形式
  else if (modifiedCode.includes('props:')) {
    modifiedCode = modifiedCode.replace(
      /props\s*:\s*\{([^}]*)\}/g,
      (_, propsContent) => {
        const trimmedContent = propsContent.trim()
        const separator = trimmedContent ? ',' : ''
        injected = true
        return `props: {${trimmedContent}${separator} refList: { type: Array, default: () => [] } }`
      }
    )
  }
  // 如果没有找到props定义，在组件对象中添加
  else if (!modifiedCode.includes('defineProps') && !modifiedCode.includes('props:')) {
    // 查找组件对象的开始位置
    const componentMatch = modifiedCode.match(new RegExp(`const\\s+${COMP_IDENTIFIER}\\s*=\\s*\\{`))
    if (componentMatch) {
      const insertPos = componentMatch.index! + componentMatch[0].length
      modifiedCode = modifiedCode.slice(0, insertPos) +
        '\n  props: { refList: { type: Array, default: () => [] } },' +
        modifiedCode.slice(insertPos)
      injected = true
    } else {
      // 如果是script setup但没有defineProps，我们需要添加一个
      if (modifiedCode.includes('setup')) {
        // 在setup函数开始处添加defineProps
        const setupMatch = modifiedCode.match(/setup\s*\([^)]*\)\s*\{/)
        if (setupMatch) {
          const insertPos = setupMatch.index! + setupMatch[0].length
          modifiedCode = modifiedCode.slice(0, insertPos) +
            '\n  const props = defineProps({ refList: { type: Array, default: () => [] } })' +
            modifiedCode.slice(insertPos)
          injected = true
        }
      }
    }
  }

  if (injected) {
    console.log('[RefList Injection] Successfully injected refList prop')
  } else {
    console.log('[RefList Injection] No suitable location found for injection')
  }

  return modifiedCode
}

async function doCompileScript(
  store: Store,
  descriptor: SFCDescriptor,
  id: string,
  ssr: boolean,
  isTS: boolean,
  isJSX: boolean,
): Promise<{ code: string; bindings: BindingMetadata | undefined; map?: any }> {
  if (descriptor.script || descriptor.scriptSetup) {
    const expressionPlugins: CompilerOptions['expressionPlugins'] = []
    if (isTS) {
      expressionPlugins.push('typescript')
    }
    if (isJSX) {
      expressionPlugins.push('jsx')
    }
    const compiledScript = store.compiler.compileScript(descriptor, {
      inlineTemplate: true,
      ...store.sfcOptions?.script,
      id,
      genDefaultAs: COMP_IDENTIFIER,
      templateOptions: {
        ...store.sfcOptions?.template,
        ssr,
        ssrCssVars: descriptor.cssVars,
        compilerOptions: {
          ...store.sfcOptions?.template?.compilerOptions,
          expressionPlugins,
        },
      },
    })
    let code = compiledScript.content

    // 注入refList到props (编译后处理，作为备用方案)
    // const originalCode = code
    // code = injectRefListToProps(code, isTS)

    // 调试信息：如果代码被修改了，输出日志
    // if (code !== originalCode) {
    //   console.log(`[RefList Injection] Modified component in ${descriptor.filename}`)
    // }

    if (isTS) {
      code = transformTS(code, isJSX)
    }
    if (compiledScript.bindings) {
      code =
        `/* Analyzed bindings: ${JSON.stringify(
          compiledScript.bindings,
          null,
          2,
        )} */\n` + code
    }

    return { code, bindings: compiledScript.bindings, map: compiledScript.map }
  } else {
    // @ts-expect-error TODO remove when 3.6 is out
    const vaporFlag = descriptor.vapor ? '__vapor: true' : ''
    return {
      code: `\nconst ${COMP_IDENTIFIER} = { ${vaporFlag} props: { refList: { type: Array, default: () => [] } }, }`,
      bindings: {},
      map: undefined,
    }
  }
}

async function doCompileTemplate(
  store: Store,
  descriptor: SFCDescriptor,
  id: string,
  bindingMetadata: BindingMetadata | undefined,
  ssr: boolean,
  isTS: boolean,
  isJSX: boolean,
) {
  const expressionPlugins: CompilerOptions['expressionPlugins'] = []
  if (isTS) {
    expressionPlugins.push('typescript')
  }
  if (isJSX) {
    expressionPlugins.push('jsx')
  }

  const res = store.compiler.compileTemplate({
    isProd: false,
    ...store.sfcOptions?.template,
    // @ts-expect-error TODO remove expect-error after 3.6
    vapor: descriptor.vapor,
    ast: descriptor.template!.ast,
    source: descriptor.template!.content,
    filename: descriptor.filename,
    id,
    scoped: descriptor.styles.some((s) => s.scoped),
    slotted: descriptor.slotted,
    ssr,
    ssrCssVars: descriptor.cssVars,
    compilerOptions: {
      ...store.sfcOptions?.template?.compilerOptions,
      bindingMetadata,
      expressionPlugins,
    },
  })
  let { code, errors, map } = res
  if (errors.length) {
    return { code, map, errors }
  }

  const fnName = ssr ? `ssrRender` : `render`

  code =
    `\n${code.replace(
      /\nexport (function|const) (render|ssrRender)/,
      `$1 ${fnName}`,
    )}` + `\n${COMP_IDENTIFIER}.${fnName} = ${fnName}`

  if (isTS) {
    code = transformTS(code, isJSX)
  }
  return { code, map, errors: [] }
}
