# Props 注入渲染器

这个功能允许你将 `injectRefListToSFC` 处理后的代码直接渲染到 Vue 的 `<component>` 组件中，实现动态渲染和实时预览。

## 功能概述

### 🎯 主要功能

1. **自动 Props 注入**: 使用 `injectRefListToSFC` 函数自动为 Vue 组件注入 `dataChart` 和 `refList` props
2. **实时渲染**: 将注入后的代码编译为可执行的 Vue 组件并立即渲染
3. **代码对比**: 显示注入前后的代码差异
4. **Props 配置**: 可自定义传递给组件的 props 数据
5. **错误处理**: 完善的错误捕获和显示机制

### 📁 相关文件

- `src/components/QuickInjectRenderer.vue` - 快速注入渲染器（推荐）
- `src/components/InjectPreviewRenderer.vue` - 完整功能渲染器
- `src/template/inject-test.vue` - 测试组件示例

## 使用方法

### 1. 在 Vue SFC Playground 中使用

1. 打开 Vue SFC Playground
2. 在编辑器中创建或编辑一个 `.vue` 文件
3. 点击输出面板的 **"inject"** 标签页
4. 系统会自动：
   - 执行 props 注入
   - 编译注入后的代码
   - 渲染组件到页面

### 2. 快速开始示例

创建一个简单的 Vue 组件：

```vue
<template>
  <div>
    <h1>我的组件</h1>
    <p>这里会自动注入 props</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const message = ref('Hello World!')
</script>
```

切换到 "inject" 标签页，你会看到：

1. **注入后的代码**:
```vue
<script setup>
import { ref } from 'vue'

const props = defineProps({ dataChart: Object, refList: [Array, Object] })
const message = ref('Hello World!')
</script>
```

2. **渲染结果**: 组件会被实时渲染，并接收到默认的 props 数据

### 3. 测试组件

使用提供的测试组件 `src/template/inject-test.vue`：

```vue
<!-- 这个组件专门用于测试 props 注入功能 -->
<template>
  <div class="inject-test">
    <h2>Props 注入测试组件</h2>
    <!-- 会显示 props 注入状态和数据 -->
  </div>
</template>
```

## 工作流程

### 1. 注入流程

```
原始 Vue 代码
    ↓
injectRefListToSFC() 函数处理
    ↓
注入 props 定义
    ↓
Vue SFC 编译器解析
    ↓
动态组件创建
    ↓
渲染到页面
```

### 2. 支持的语法

#### ✅ Script Setup 语法
```vue
<script setup>
// 会自动注入：
// const props = defineProps({ dataChart: Object, refList: [Array, Object] })
</script>
```

#### ✅ Options API 语法
```vue
<script>
export default {
  // 会自动注入：
  // props: { dataChart: Object, refList: [Array, Object] }
}
</script>
```

#### ✅ TypeScript 支持
```vue
<script setup lang="ts">
// 会自动注入：
// const props = defineProps<{ dataChart?: Object, refList?: Array<any> | Object }>()
</script>
```

## 界面功能

### 🔧 工具栏

- **"应用注入并渲染"**: 手动触发注入和渲染过程
- **"显示/隐藏代码"**: 切换代码对比视图
- **状态指示器**: 显示当前处理状态（等待/处理中/成功/失败）

### 📊 代码对比

显示注入前后的代码差异：
- **左侧**: 原始代码
- **右侧**: 注入后的代码
- **高亮**: 变化的部分

### 🎮 Props 配置

可以自定义传递给组件的数据：

```json
// dataChart 示例
{
  "type": "bar",
  "data": [15, 25, 35, 45, 55],
  "labels": ["Mon", "Tue", "Wed", "Thu", "Fri"]
}

// refList 示例
[
  { "id": 1, "name": "Task 1", "completed": false },
  { "id": 2, "name": "Task 2", "completed": true },
  { "id": 3, "name": "Task 3", "completed": false }
]
```

### 🐛 错误处理

系统会捕获并显示以下错误：
- **解析错误**: SFC 语法错误
- **编译错误**: Vue 编译失败
- **运行时错误**: 组件执行错误

## 高级功能

### 1. 自动文件监听

当你修改编辑器中的代码时，渲染器会自动：
- 清除之前的渲染结果
- 重新执行注入流程
- 更新渲染结果

### 2. 性能优化

- **组件缓存**: 避免重复编译相同的代码
- **增量更新**: 只在代码变化时重新处理
- **错误恢复**: 错误后可以继续正常工作

### 3. 调试支持

- **详细日志**: 在控制台输出处理过程
- **状态追踪**: 实时显示处理状态
- **错误定位**: 精确的错误信息和位置

## 实际应用场景

### 1. 组件开发

在开发需要接收 `dataChart` 和 `refList` props 的组件时：
- 无需手动添加 props 定义
- 实时预览组件效果
- 快速测试不同的 props 数据

### 2. 代码演示

向他人展示 props 注入功能：
- 直观的前后对比
- 实时的渲染效果
- 清晰的状态反馈

### 3. 调试和测试

排查 props 相关问题：
- 验证注入是否成功
- 测试组件对不同数据的响应
- 检查类型兼容性

## 注意事项

### ⚠️ 限制

1. **文件类型**: 只支持 `.vue` 文件
2. **语法要求**: 需要有效的 Vue SFC 语法
3. **Props 冲突**: 如果组件已定义同名 props，可能会冲突

### 💡 最佳实践

1. **测试先行**: 使用测试组件验证功能
2. **渐进增强**: 从简单组件开始测试
3. **错误处理**: 注意查看错误信息和控制台日志
4. **数据格式**: 确保 props 数据格式正确

### 🔧 故障排除

**问题**: 组件没有渲染
- 检查是否是有效的 `.vue` 文件
- 查看控制台错误信息
- 确认代码语法正确

**问题**: Props 没有注入
- 检查组件是否已有同名 props
- 查看注入日志信息
- 尝试手动触发渲染

**问题**: 渲染错误
- 检查 props 数据格式
- 查看组件内部逻辑
- 使用测试组件对比

这个系统让你可以轻松地测试和预览 props 注入功能，大大提高了开发效率！
